﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - 口袋后台管理系统</title>
    
    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/BMS.styles.css" asp-append-version="true" />
    
    <!-- Axios -->
    <script src="~/lib/axios.min.js"></script>
    <!-- Vue.js 3 -->
    <script src="~/lib/vue/vue.global.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --nav-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #2d3748;
            line-height: 1.6;
        }

        /* 现代化导航栏 */
        .navbar-modern {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: var(--nav-shadow);
            padding: 12px 0;
            transition: var(--transition);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.4rem;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-decoration: none;
            transition: var(--transition);
        }

        .navbar-brand:hover {
            transform: translateY(-1px);
            text-decoration: none;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            color: #4a5568 !important;
            padding: 10px 16px !important;
            border-radius: 8px;
            margin: 0 4px;
            transition: var(--transition);
            position: relative;
        }

        .navbar-nav .nav-link:hover {
            color: #667eea !important;
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .navbar-nav .nav-link.active {
            color: #667eea !important;
            background: rgba(102, 126, 234, 0.1);
        }

        .navbar-nav .nav-link i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 下拉菜单现代化 */
        .dropdown-menu {
            border: none;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.12);
            padding: 8px;
            margin-top: 8px;
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
        }

        .dropdown-item {
            border-radius: 8px;
            padding: 12px 16px;
            transition: var(--transition);
            font-weight: 500;
            color: #4a5568;
            margin-bottom: 2px;
        }

        .dropdown-item:hover {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateX(4px);
        }

        .dropdown-item i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            opacity: 0.7;
        }

        .dropdown-divider {
            margin: 8px 0;
            border-color: rgba(0, 0, 0, 0.08);
        }

        /* 用户下拉菜单 */
        .dropdown-toggle::after {
            margin-left: 8px;
            opacity: 0.6;
        }

        /* 主内容区域 */
        .main-content {
            min-height: calc(100vh - 140px);
            padding: 30px 0;
        }

        /* 现代化页脚 */
        .footer-modern {
            background: white;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 25px 0;
            color: #718096;
            font-size: 0.9rem;
            margin-top: auto;
        }

        .footer-modern a {
            color: #667eea;
            text-decoration: none;
            transition: var(--transition);
        }

        .footer-modern a:hover {
            color: #5a67d8;
            text-decoration: none;
        }

        /* 响应式优化 */
        @@media (max-width: 991.98px) {
            .navbar-nav {
                padding: 10px 0;
            }
            
            .navbar-nav .nav-link {
                margin: 2px 0;
            }
            
            .dropdown-menu {
                box-shadow: none;
                background: #f8fafc;
                border: 1px solid #e2e8f0;
            }
        }

        /* 加载动画 */
        .page-loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
            z-index: 9999;
        }

        .page-loading.loading {
            animation: loading-bar 2s ease-in-out;
        }

        @@keyframes loading-bar {
            0% { transform: scaleX(0); }
            50% { transform: scaleX(0.7); }
            100% { transform: scaleX(1); }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 面包屑导航 */
        .breadcrumb-modern {
            background: none;
            padding: 0;
            margin: 0 0 20px 0;
        }

        .breadcrumb-modern .breadcrumb-item {
            color: #718096;
            font-size: 0.9rem;
        }

        .breadcrumb-modern .breadcrumb-item.active {
            color: #2d3748;
            font-weight: 500;
        }

        .breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: #cbd5e0;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <!-- 页面加载指示器 -->
    <div class="page-loading" id="pageLoading"></div>

    <header>
        <nav class="navbar navbar-expand-lg navbar-light navbar-modern fixed-top">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-cube"></i> 口袋后台管理系统
                </a>
                
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" 
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="navbar-collapse collapse d-lg-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home"></i> 首页
                            </a>
                        </li>
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="systemDropdown" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-cogs"></i> 系统管理
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="systemDropdown">
                                    <li><a class="dropdown-item" asp-controller="AdminBm" asp-action="Index">
                                        <i class="fas fa-users-cog"></i> 管理员管理
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="showDevelopingAlert()">
                                        <i class="fas fa-key"></i> 权限管理
                                    </a></li>
                                </ul>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="gameDropdown" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-gamepad"></i> 游戏管理
                                </a>
                                <ul class="dropdown-menu" aria-labelledby="gameDropdown">
                                    <li><h6 class="dropdown-header">用户管理</h6></li>
                                    <li><a class="dropdown-item" asp-controller="User" asp-action="Index">
                                        <i class="fas fa-users"></i> 用户管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="UserPet" asp-action="Index">
                                        <i class="fas fa-paw"></i> 用户宠物管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="UserItem" asp-action="Index">
                                        <i class="fas fa-boxes"></i> 用户道具管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="UserEquipment" asp-action="Index">
                                        <i class="fas fa-shield-alt"></i> 用户装备管理
                                    </a></li>
                                    
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">游戏配置</h6></li>
                                    <li><a class="dropdown-item" asp-controller="MapConfig" asp-action="Index">
                                        <i class="fas fa-map"></i> 地图配置
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="MapDetail" asp-action="Index">
                                        <i class="fas fa-map-marked"></i> 地图详情配置
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="MapMonster" asp-action="Index">
                                        <i class="fas fa-dragon"></i> 地图配置怪物
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="PetConfig" asp-action="Index">
                                        <i class="fas fa-dog"></i> 宠物配置管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="MonsterConfig" asp-action="Index">
                                        <i class="fas fa-spider"></i> 怪物配置管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="ItemConfig" asp-action="Index">
                                        <i class="fas fa-cubes"></i> 道具配置管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="DropConfig" asp-action="Index">
                                        <i class="fas fa-gift"></i> 掉落配置管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="TaskConfig" asp-action="Index">
                                        <i class="fas fa-tasks"></i> 任务配置管理
                                    </a></li>

                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">高级配置</h6></li>
                                    <li><a class="dropdown-item" asp-controller="Equipment" asp-action="Index">
                                        <i class="fas fa-cog"></i> 装备配置管理
                                    </a></li>
                                    <li><a class="dropdown-item" asp-controller="Skill" asp-action="Index">
                                        <i class="fas fa-magic"></i> 技能配置管理
                                    </a></li>
                                </ul>
                            </li>
                        }
                    </ul>
                    <ul class="navbar-nav">
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" 
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-circle"></i> @User.Identity.Name
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="#" onclick="showDevelopingAlert()">
                                        <i class="fas fa-user-edit"></i> 个人设置
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="showDevelopingAlert()">
                                        <i class="fas fa-lock"></i> 修改密码
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-controller="Auth" asp-action="Logout">
                                        <i class="fas fa-sign-out-alt"></i> 退出登录
                                    </a></li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Auth" asp-action="Login">
                                    <i class="fas fa-sign-in-alt"></i> 登录
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="container-fluid main-content" style="margin-top: 80px;">
        <main role="main" class="pb-3">
            @RenderBody()
        </main>
    </div>

    <footer class="footer-modern">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">
                        &copy; 2025 口袋后台管理系统 - 
                        <a asp-area="" asp-controller="Home" asp-action="Privacy">隐私政策</a>
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <small>
                        <i class="fas fa-code"></i> 基于 ASP.NET Core & Vue.js 构建
                    </small>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Razor安全工具类 -->
    <script src="~/js/razor-safe.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    
    @await RenderSectionAsync("Scripts", required: false)
    
    <!-- 全局脚本 -->
    <script>
        // 配置 Toastr
        if (typeof toastr !== 'undefined') {
            toastr.options = {
                closeButton: true,
                progressBar: true,
                positionClass: "toast-top-right",
                timeOut: 3000,
                extendedTimeOut: 1000,
                showEasing: "swing",
                hideEasing: "linear",
                showMethod: "fadeIn",
                hideMethod: "fadeOut"
            };
        }

        // 显示开发中提示
        function showDevelopingAlert() {
            if (typeof toastr !== 'undefined') {
                toastr.info('功能开发中，敬请期待！', '提示', {
                    timeOut: 2000,
                    progressBar: true
                });
            } else {
                alert('功能开发中...');
            }
        }

        // 页面加载效果
        document.addEventListener('DOMContentLoaded', function() {
            const loadingBar = document.getElementById('pageLoading');
            if (loadingBar) {
                loadingBar.classList.add('loading');
                
                // 2秒后隐藏加载条
                setTimeout(() => {
                    loadingBar.style.opacity = '0';
                    setTimeout(() => {
                        loadingBar.style.display = 'none';
                    }, 300);
                }, 2000);
            }

            // 为当前页面的导航项添加活动状态
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
            
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航栏滚动效果
        let lastScrollTop = 0;
        const navbar = document.querySelector('.navbar-modern');
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // 向下滚动时隐藏导航栏
                navbar.style.transform = 'translateY(-100%)';
            } else {
                // 向上滚动时显示导航栏
                navbar.style.transform = 'translateY(0)';
            }
            
            // 滚动时添加阴影效果
            if (scrollTop > 10) {
                navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
            } else {
                navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.08)';
            }
            
            lastScrollTop = scrollTop;
        });
    </script>
</body>
</html>
