@{
    ViewData["Title"] = "用户装备管理";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --card-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        --card-hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        --border-radius: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modern-page {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .modern-card:hover {
        box-shadow: var(--card-hover-shadow);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 1.5rem;
    }

    .modern-card-title {
        font-weight: 600;
        font-size: 1.25rem;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1em;
    }

    .modern-btn {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .modern-btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }

    .modern-btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .modern-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.3);
        color: white;
    }

    .modern-btn-warning {
        background: var(--warning-gradient);
        color: white;
    }

    .modern-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        color: white;
    }

    .modern-btn-info {
        background: var(--info-gradient);
        color: white;
    }

    .modern-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        color: white;
    }

    .modern-btn-danger {
        background: var(--danger-gradient);
        color: white;
    }

    .modern-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
        color: white;
    }

    .modern-form-control {
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
    }

    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        background: white;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: none;
        font-weight: 600;
        color: #475569;
        padding: 1rem;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.001);
    }

    .modern-table tbody td {
        padding: 1rem;
        border-color: #f1f5f9;
    }

    .modern-pagination {
        gap: 0.5rem;
    }

    .modern-pagination .page-link {
        border-radius: 10px;
        border: none;
        margin: 0 2px;
        padding: 0.6rem 1rem;
        transition: var(--transition);
    }

    .modern-pagination .page-item.active .page-link {
        background: var(--primary-gradient);
        border: none;
    }

    .modern-pagination .page-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .modern-breadcrumb {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 0;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }

    .fade-in-delay-1 {
        animation: fadeInUp 0.6s ease 0.1s forwards;
        opacity: 0;
    }

    .fade-in-delay-2 {
        animation: fadeInUp 0.6s ease 0.2s forwards;
        opacity: 0;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-header h1 {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }
</style>

<div class="modern-page">

<!-- 用户装备管理应用容器 -->
<div id="userEquipmentApp">
    <!-- 页面标题 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-shield-alt mr-2"></i>用户装备管理
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首页</a></li>
                        <li class="breadcrumb-item active">用户装备管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="content">
        <div class="container-fluid">
            
            <!-- 搜索条件 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-search mr-1"></i>搜索条件
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchUserId">用户ID</label>
                                <input type="number" class="form-control" id="searchUserId" v-model="queryForm.userId" placeholder="请输入用户ID">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchUserName">用户名</label>
                                <input type="text" class="form-control" id="searchUserName" v-model="queryForm.userName" placeholder="请输入用户名">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchEquipmentName">装备名称</label>
                                <input type="text" class="form-control" id="searchEquipmentName" v-model="queryForm.name" placeholder="请输入装备名称">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchEquipmentType">装备类型</label>
                                <select class="form-control" id="searchEquipmentType" v-model="queryForm.equipTypeId">
                                    <option value="">全部类型</option>
                                    <option v-for="option in equipmentTypeOptions" v-bind:key="option.value" v-bind:value="option.value">{{ option.label }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchEquipmentStatus">装备状态</label>
                                <select class="form-control" id="searchEquipmentStatus" v-model="queryForm.isEquipped">
                                    <option value="">全部状态</option>
                                    <option value="true">已装备</option>
                                    <option value="false">未装备</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchMinLevel">最低强化等级</label>
                                <input type="number" class="form-control" id="searchMinLevel" v-model="queryForm.minStrengthenLevel" placeholder="最低等级" min="0" max="20">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="searchMaxLevel">最高强化等级</label>
                                <input type="number" class="form-control" id="searchMaxLevel" v-model="queryForm.maxStrengthenLevel" placeholder="最高等级" min="0" max="20">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div>
                                    <button type="button" class="btn btn-primary mr-2" v-on:click="searchEquipments">
                                        <i class="fas fa-search mr-1"></i>搜索
                                    </button>
                                    <button type="button" class="btn btn-secondary mr-2" v-on:click="resetSearch">
                                        <i class="fas fa-redo mr-1"></i>重置
                                    </button>
                                    <button type="button" class="btn btn-success" v-on:click="showAddModal">
                                        <i class="fas fa-plus mr-1"></i>新增装备
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 装备列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list mr-1"></i>用户装备列表
                    </h3>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>装备名称</th>
                                <th>装备类型</th>
                                <th>强化等级</th>
                                <th>槽位</th>
                                <th>位置</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody v-if="loading">
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else-if="equipmentsList.length === 0">
                            <tr>
                                <td colspan="10" class="text-center py-4 text-muted">
                                    <i class="fas fa-inbox mr-2"></i>暂无数据
                                </td>
                            </tr>
                        </tbody>
                        <tbody v-else>
                            <tr v-for="equipment in equipmentsList" v-bind:key="equipment.id">
                                <td>{{ equipment.id }}</td>
                                <td>
                                    <span class="text-muted">#{{ equipment.userId }}</span><br>
                                    <strong>{{ equipment.userName }}</strong>
                                </td>
                                <td>
                                    <i class="fas fa-shield-alt mr-1 text-primary"></i>
                                    {{ equipment.name }}
                                </td>
                                <td>{{ equipment.equipTypeName }}</td>
                                <td>
                                    <span v-bind:class="getStrengthenBadgeClass(equipment.strengthenLevel)">
                                        +{{ equipment.strengthenLevel }}
                                    </span>
                                </td>
                                <td>{{ equipment.slot }}</td>
                                <td>{{ equipment.position }}</td>
                                <td>
                                    <span v-if="equipment.isEquipped" class="badge-success">
                                        <i class="fas fa-check mr-1"></i>已装备
                                    </span>
                                    <span v-else class="badge-secondary">
                                        <i class="fas fa-times mr-1"></i>未装备
                                    </span>
                                </td>
                                <td>{{ formatDateTime(equipment.createTime) }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-info btn-sm" v-on:click="viewDetail(equipment)" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm" v-on:click="showEditModal(equipment)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-primary btn-sm" v-on:click="showStrengthenModal(equipment)" title="强化">
                                            <i class="fas fa-hammer"></i>
                                        </button>
                                        <button v-if="!equipment.isEquipped" type="button" class="btn btn-success btn-sm" v-on:click="wearEquipment(equipment)" title="穿戴">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button v-else type="button" class="btn btn-secondary btn-sm" v-on:click="unwearEquipment(equipment)" title="卸下">
                                            <i class="fas fa-minus-circle"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm" v-on:click="deleteEquipment(equipment)" v-bind:disabled="equipment.isEquipped" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="card-footer" v-if="equipmentsList.length > 0">
                    <div class="row align-items-center">
                        <div class="col-sm-6">
                            <div class="dataTables_info">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="dataTables_paginate paging_simple_numbers float-right">
                                <ul class="pagination">
                                    <li class="paginate_button page-item previous" v-bind:class="{ disabled: currentPage === 1 }">
                                        <a href="#" aria-controls="example2" data-dt-idx="0" tabindex="0" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li v-for="page in visiblePages" v-bind:key="page" class="paginate_button page-item" v-bind:class="{ active: page === currentPage }">
                                        <a href="#" aria-controls="example2" tabindex="0" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="paginate_button page-item next" v-bind:class="{ disabled: currentPage === totalPages }">
                                        <a href="#" aria-controls="example2" data-dt-idx="7" tabindex="0" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </div>

    <!-- 新增/编辑装备模态框 -->
    <div class="modal fade" id="equipmentModal" tabindex="-1" role="dialog" aria-labelledby="equipmentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentModalLabel">
                        <i class="fas fa-shield-alt mr-2"></i>{{ isEdit ? '编辑装备' : '新增装备' }}
                    </h5>
                    <button type="button" class="close" v-on:click="closeModal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modalUserId">所属用户 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="modalUserId" v-model="equipmentForm.userId" required>
                                        <option value="">请选择用户</option>
                                        <option v-for="option in userOptions" v-bind:key="option.value" v-bind:value="option.value">{{ option.label }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modalEquipId">装备配置 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="modalEquipId" v-model="equipmentForm.equipId" required v-on:change="onEquipmentConfigChange">
                                        <option value="">请选择装备配置</option>
                                        <option v-for="option in equipmentConfigOptions" v-bind:key="option.value" v-bind:value="option.value" v-bind:data-name="option.name" v-bind:data-icon="option.icon" v-bind:data-equip-type-id="option.equipTypeId">{{ option.label }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modalStrengthenLevel">强化等级</label>
                                    <input type="number" class="form-control" id="modalStrengthenLevel" v-model="equipmentForm.strengthenLevel" min="0" max="20" placeholder="0-20">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modalSlot">槽位</label>
                                    <input type="number" class="form-control" id="modalSlot" v-model="equipmentForm.slot" min="1" placeholder="槽位编号">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modalPosition">位置</label>
                                    <input type="number" class="form-control" id="modalPosition" v-model="equipmentForm.position" min="1" placeholder="位置编号">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="modalIsEquipped">装备状态</label>
                                    <select class="form-control" id="modalIsEquipped" v-model="equipmentForm.isEquipped">
                                        <option v-bind:value="false">未装备</option>
                                        <option v-bind:value="true">已装备</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" v-on:click="closeModal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-primary" v-on:click="saveEquipment" v-bind:disabled="saving">
                        <i v-if="saving" class="fas fa-spinner fa-spin mr-1"></i>
                        <i v-else class="fas fa-save mr-1"></i>
                        {{ saving ? '保存中...' : '保存' }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 装备详情模态框 -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1" role="dialog" aria-labelledby="equipmentDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailModalLabel">
                        <i class="fas fa-info-circle mr-2"></i>装备详情
                    </h5>
                    <button type="button" class="close" v-on:click="closeDetailModal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" v-if="equipmentDetail">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">基础信息</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>装备ID:</strong></td>
                                    <td>{{ equipmentDetail.id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>用户:</strong></td>
                                    <td>{{ equipmentDetail.userName }} (#{{ equipmentDetail.userId }})</td>
                                </tr>
                                <tr>
                                    <td><strong>装备名称:</strong></td>
                                    <td>{{ equipmentDetail.name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>装备类型:</strong></td>
                                    <td>{{ equipmentDetail.equipTypeName }}</td>
                                </tr>
                                <tr>
                                    <td><strong>强化等级:</strong></td>
                                    <td>
                                        <span v-bind:class="getStrengthenBadgeClass(equipmentDetail.strengthenLevel)">
                                            +{{ equipmentDetail.strengthenLevel }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>槽位:</strong></td>
                                    <td>{{ equipmentDetail.slot }}</td>
                                </tr>
                                <tr>
                                    <td><strong>位置:</strong></td>
                                    <td>{{ equipmentDetail.position }}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态:</strong></td>
                                    <td>
                                        <span v-if="equipmentDetail.isEquipped" class="badge-success">已装备</span>
                                        <span v-else class="badge-secondary">未装备</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6" v-if="equipmentDetail.detail">
                            <h6 class="text-success">装备属性</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>攻击力:</strong></td>
                                    <td class="text-danger">{{ equipmentDetail.detail.atk }}</td>
                                </tr>
                                <tr>
                                    <td><strong>命中:</strong></td>
                                    <td class="text-warning">{{ equipmentDetail.detail.hit }}</td>
                                </tr>
                                <tr>
                                    <td><strong>防御力:</strong></td>
                                    <td class="text-primary">{{ equipmentDetail.detail.def }}</td>
                                </tr>
                                <tr>
                                    <td><strong>速度:</strong></td>
                                    <td class="text-info">{{ equipmentDetail.detail.spd }}</td>
                                </tr>
                                <tr>
                                    <td><strong>闪避:</strong></td>
                                    <td class="text-success">{{ equipmentDetail.detail.dodge }}</td>
                                </tr>
                                <tr>
                                    <td><strong>生命值:</strong></td>
                                    <td class="text-danger">{{ equipmentDetail.detail.hp }}</td>
                                </tr>
                                <tr>
                                    <td><strong>魔法值:</strong></td>
                                    <td class="text-primary">{{ equipmentDetail.detail.mp }}</td>
                                </tr>
                                <tr>
                                    <td><strong>深度:</strong></td>
                                    <td>{{ equipmentDetail.detail.deepen }}</td>
                                </tr>
                                <tr>
                                    <td><strong>偏移:</strong></td>
                                    <td>{{ equipmentDetail.detail.offset }}</td>
                                </tr>
                                <tr>
                                    <td><strong>吸血:</strong></td>
                                    <td>{{ equipmentDetail.detail.vamp }}</td>
                                </tr>
                                <tr>
                                    <td><strong>吸魔:</strong></td>
                                    <td>{{ equipmentDetail.detail.vampMp }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row" v-if="equipmentDetail.detail && equipmentDetail.detail.description">
                        <div class="col-12">
                            <h6 class="text-info">装备描述</h6>
                            <p class="text-muted">{{ equipmentDetail.detail.description }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" v-on:click="closeDetailModal">
                        <i class="fas fa-times mr-1"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 强化装备模态框 -->
    <div class="modal fade" id="strengthenModal" tabindex="-1" role="dialog" aria-labelledby="strengthenModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="strengthenModalLabel">
                        <i class="fas fa-hammer mr-2"></i>强化装备
                    </h5>
                    <button type="button" class="close" v-on:click="closeStrengthenModal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" v-if="selectedEquipment">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        当前装备：<strong>{{ selectedEquipment.name }}</strong><br>
                        当前强化等级：<span v-bind:class="getStrengthenBadgeClass(selectedEquipment.strengthenLevel)">+{{ selectedEquipment.strengthenLevel }}</span>
                    </div>
                    <div class="form-group">
                        <label for="targetLevel">目标强化等级 <span class="text-danger">*</span></label>
                        <input type="number" class="form-control" id="targetLevel" v-model="strengthenForm.targetLevel" 
                               v-bind:min="selectedEquipment.strengthenLevel + 1" max="20" required>
                        <small class="form-text text-muted">强化等级范围：{{ selectedEquipment.strengthenLevel + 1 }} - 20</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" v-on:click="closeStrengthenModal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-warning" v-on:click="confirmStrengthen" v-bind:disabled="saving">
                        <i v-if="saving" class="fas fa-spinner fa-spin mr-1"></i>
                        <i v-else class="fas fa-hammer mr-1"></i>
                        {{ saving ? '强化中...' : '确认强化' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,
                    
                    // 装备列表数据
                    equipmentsList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,
                    
                    // 表单状态
                    isEdit: false,
                    selectedEquipment: null,
                    equipmentDetail: null,
                    
                    // 下拉选项
                    userOptions: [],
                    equipmentTypeOptions: [],
                    equipmentConfigOptions: [],
                    
                    // 查询表单
                    queryForm: {
                        userId: null,
                        userName: '',
                        name: '',
                        equipTypeId: '',
                        isEquipped: '',
                        minStrengthenLevel: null,
                        maxStrengthenLevel: null
                    },
                    
                    // 装备表单
                    equipmentForm: {
                        id: 0,
                        userId: null,
                        equipId: null,
                        name: '',
                        icon: '',
                        equipTypeId: null,
                        strengthenLevel: 0,
                        slot: 1,
                        position: 1,
                        isEquipped: false
                    },
                    
                    // 强化表单
                    strengthenForm: {
                        id: 0,
                        targetLevel: 1
                    }
                };
            },
            
            computed: {
                // 总页数
                totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },
                
                // 可见页码
                visiblePages() {
                    const total = this.totalPages;
                    const current = this.currentPage;
                    const delta = 2;
                    const pages = [];
                    
                    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                        pages.push(i);
                    }
                    
                    if (current - delta > 2) {
                        pages.unshift("...");
                    }
                    if (current + delta < total - 1) {
                        pages.push("...");
                    }
                    
                    pages.unshift(1);
                    if (total > 1) {
                        pages.push(total);
                    }
                    
                    return pages.filter((page, index, arr) => arr.indexOf(page) === index);
                }
            },
            
            methods: {
                // 加载装备列表
                async loadEquipments() {
                    try {
                        this.loading = true;
                        
                        // 准备请求数据，确保字段名与后端DTO匹配
                        const requestData = {
                            UserId: this.queryForm.userId,
                            UserName: this.queryForm.userName || '',
                            Name: this.queryForm.name || '',
                            EquipTypeId: this.queryForm.equipTypeId || null,  // 保持字符串类型，不转换为整数
                            IsEquipped: this.queryForm.isEquipped === '' ? null : (this.queryForm.isEquipped === 'true'),
                            MinStrengthenLevel: this.queryForm.minStrengthenLevel,
                            MaxStrengthenLevel: this.queryForm.maxStrengthenLevel,
                            Page: this.currentPage,
                            PageSize: this.pageSize
                        };

                        // 添加调试日志
                        console.log('发送请求数据:', requestData);
                        
                        const response = await axios.post('/UserEquipment/GetList', requestData);
                        if (response.data.code === 200) {
                            this.equipmentsList = response.data.data || [];
                            this.totalCount = response.data.total || 0;
                        } else {
                            console.error('获取装备列表失败：', response.data.message);
                        }
                    } catch (error) {
                        console.error('获取装备列表失败：', error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 搜索装备
                searchEquipments() {
                    console.log('搜索参数:', this.queryForm);
                    console.log('装备类型ID:', this.queryForm.equipTypeId, '类型:', typeof this.queryForm.equipTypeId);
                    this.currentPage = 1;
                    this.loadEquipments();
                },
                
                // 重置搜索
                resetSearch() {
                    Object.assign(this.queryForm, {
                        userId: null,
                        userName: '',
                        name: '',
                        equipTypeId: '',
                        isEquipped: '',
                        minStrengthenLevel: null,
                        maxStrengthenLevel: null
                    });
                    this.searchEquipments();
                },
                
                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadEquipments();
                    }
                },
                
                // 显示新增模态框
                showAddModal() {
                    this.isEdit = false;
                    this.equipmentForm = {
                        id: 0,
                        userId: null,
                        equipId: null,
                        name: '',
                        icon: '',
                        equipTypeId: null,
                        strengthenLevel: 0,
                        slot: 1,
                        position: 1,
                        isEquipped: false
                    };
                    $('#equipmentModal').modal('show');
                },
                
                // 显示编辑模态框
                showEditModal(equipment) {
                    this.isEdit = true;
                    this.equipmentForm = {
                        id: equipment.id,
                        userId: equipment.userId,
                        equipId: equipment.equipId,
                        name: equipment.name,
                        icon: equipment.icon,
                        equipTypeId: equipment.equipTypeId,
                        strengthenLevel: equipment.strengthenLevel,
                        slot: equipment.slot,
                        position: equipment.position,
                        isEquipped: equipment.isEquipped
                    };
                    $('#equipmentModal').modal('show');
                },
                
                // 保存装备
                async saveEquipment() {
                    try {
                        this.saving = true;
                        
                        const url = this.isEdit ? '/UserEquipment/Update' : '/UserEquipment/Create';
                        const response = await axios.post(url, this.equipmentForm);
                        
                        if (response.data.code === 200) {
                            $('#equipmentModal').modal('hide');
                            this.loadEquipments();
                            alert(response.data.message || (this.isEdit ? '更新成功' : '创建成功'));
                        } else {
                            alert(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存装备失败：', error);
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 删除装备
                async deleteEquipment(equipment) {
                    if (equipment.isEquipped) {
                        alert('已装备的装备无法删除，请先卸下装备');
                        return;
                    }
                    
                    if (!confirm(`确定要删除装备"${equipment.name}"吗？此操作无法撤销！`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Delete', { id: equipment.id });
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('删除成功');
                        } else {
                            alert(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除装备失败：', error);
                        alert('删除失败，请重试');
                    }
                },
                
                // 查看装备详情
                async viewDetail(equipment) {
                    try {
                        const response = await axios.get(`/UserEquipment/GetById/${equipment.id}`);
                        if (response.data.code === 200) {
                            this.equipmentDetail = response.data.data;
                            $('#equipmentDetailModal').modal('show');
                        } else {
                            alert(response.data.message || '获取装备详情失败');
                        }
                    } catch (error) {
                        console.error('获取装备详情失败：', error);
                        alert('获取装备详情失败，请重试');
                    }
                },
                
                // 显示强化模态框
                showStrengthenModal(equipment) {
                    this.selectedEquipment = equipment;
                    this.strengthenForm = {
                        id: equipment.id,
                        targetLevel: equipment.strengthenLevel + 1
                    };
                    $('#strengthenModal').modal('show');
                },
                
                // 确认强化
                async confirmStrengthen() {
                    if (!confirm(`确定要将装备强化到 +${this.strengthenForm.targetLevel} 级吗？`)) {
                        return;
                    }
                    
                    try {
                        this.saving = true;
                        const response = await axios.post('/UserEquipment/Strengthen', this.strengthenForm);
                        
                        if (response.data.code === 200) {
                            $('#strengthenModal').modal('hide');
                            this.loadEquipments();
                            alert(response.data.message || '强化成功');
                        } else {
                            alert(response.data.message || '强化失败');
                        }
                    } catch (error) {
                        console.error('强化装备失败：', error);
                        alert('强化失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },
                
                // 穿戴装备
                async wearEquipment(equipment) {
                    if (!confirm(`确定要穿戴装备"${equipment.name}"吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Wear', {
                            id: equipment.id,
                            userId: equipment.userId,
                            position: equipment.position
                        });
                        
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('装备穿戴成功');
                        } else {
                            alert(response.data.message || '穿戴失败');
                        }
                    } catch (error) {
                        console.error('穿戴装备失败：', error);
                        alert('穿戴失败，请重试');
                    }
                },
                
                // 卸下装备
                async unwearEquipment(equipment) {
                    if (!confirm(`确定要卸下装备"${equipment.name}"吗？`)) {
                        return;
                    }
                    
                    try {
                        const response = await axios.post('/UserEquipment/Unwear', null, {
                            params: { equipmentId: equipment.id }
                        });
                        
                        if (response.data.code === 200) {
                            this.loadEquipments();
                            alert('装备卸下成功');
                        } else {
                            alert(response.data.message || '卸下失败');
                        }
                    } catch (error) {
                        console.error('卸下装备失败：', error);
                        alert('卸下失败，请重试');
                    }
                },
                
                // 获取强化等级徽章样式
                getStrengthenBadgeClass(level) {
                    if (level >= 15) return 'badge-danger';
                    if (level >= 10) return 'badge-warning';
                    if (level >= 5) return 'badge-info';
                    return 'badge-secondary';
                },
                
                // 装备配置选择变化时
                onEquipmentConfigChange() {
                    if (!this.equipmentForm.equipId) return;
                    
                    // 从装备配置选项中找到选中的配置
                    const selectedConfig = this.equipmentConfigOptions.find(option => option.value === this.equipmentForm.equipId);
                    if (selectedConfig) {
                        // 自动填充装备信息
                        this.equipmentForm.name = selectedConfig.name;
                        this.equipmentForm.icon = selectedConfig.icon;
                        this.equipmentForm.equipTypeId = selectedConfig.equipTypeId;
                    }
                },
                
                // 格式化日期时间
                formatDateTime(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },
                
                // 加载下拉选项
                async loadOptions() {
                    try {
                        // 加载用户选项
                        const userResponse = await axios.get('/UserEquipment/GetUserOptions');
                        if (userResponse.data.code === 200) {
                            this.userOptions = userResponse.data.data;
                        }

                        // 加载装备类型选项
                        const typeResponse = await axios.get('/UserEquipment/GetEquipmentTypeOptions');
                        if (typeResponse.data.code === 200) {
                            this.equipmentTypeOptions = typeResponse.data.data;
                        }

                        // 加载装备配置选项
                        const configResponse = await axios.get('/UserEquipment/GetEquipmentConfigOptions');
                        if (configResponse.data.code === 200) {
                            this.equipmentConfigOptions = configResponse.data.data;
                        }
                    } catch (error) {
                        console.error('加载选项失败：', error);
                    }
                },
                
                // 关闭装备模态框
                closeModal() {
                    $('#equipmentModal').modal('hide');
                },
                
                // 关闭装备详情模态框
                closeDetailModal() {
                    $('#equipmentDetailModal').modal('hide');
                },
                
                // 关闭强化模态框
                closeStrengthenModal() {
                    $('#strengthenModal').modal('hide');
                }
            },
            
            mounted() {
                // 初始化加载
                this.loadOptions();
                this.loadEquipments();
            }
        }).mount('#userEquipmentApp');
    </script>
}