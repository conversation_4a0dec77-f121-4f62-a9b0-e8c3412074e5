@{
    ViewData["Title"] = "地图怪物管理";
}

<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>地图怪物管理</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">首页</a></li>
                        <li class="breadcrumb-item active">地图怪物管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="card" id="mapMonsterApp">
                <div class="card-header">
                    <h3 class="card-title">地图怪物列表</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary" v-on:click="showCreateModal">
                            <i class="fas fa-plus"></i> 新增地图怪物
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="mapSearch">地图选择：</label>
                            <select class="form-control" v-model="searchForm.MapId" id="mapSearch">
                                <option value="">请选择地图</option>
                                <option v-for="map in maps" :key="map.mapId" :value="map.mapId">
                                    {{ map.mapName }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="monsterNameSearch">怪物名称：</label>
                            <input type="text" class="form-control" v-model="searchForm.MonsterName" 
                                   placeholder="请输入怪物名称" id="monsterNameSearch">
                        </div>
                        <div class="col-md-3">
                            <label for="elementSearch">五行属性：</label>
                            <select class="form-control" v-model="searchForm.Element" id="elementSearch">
                                <option value="">请选择五行</option>
                                <option value="金">金</option>
                                <option value="木">木</option>
                                <option value="水">水</option>
                                <option value="火">火</option>
                                <option value="土">土</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-info me-2" v-on:click="searchMapMonsters">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-secondary" v-on:click="resetSearch">
                                <i class="fas fa-refresh"></i> 重置
                            </button>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>地图ID</th>
                                    <th>地图名称</th>
                                    <th>怪物序号</th>
                                    <th>怪物名称</th>
                                    <th>五行属性</th>
                                    <th>等级范围</th>
                                    <th>HP</th>
                                    <th>攻击力</th>
                                    <th>防御力</th>
                                    <th>经验值</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="loading">
                                    <td colspan="12" class="text-center">数据加载中...</td>
                                </tr>
                                <tr v-else-if="mapMonsters.length === 0">
                                    <td colspan="12" class="text-center">暂无数据</td>
                                </tr>
                                <tr v-else v-for="monster in mapMonsters" :key="monster.id">
                                    <td>{{ monster.id }}</td>
                                    <td>{{ monster.mapId }}</td>
                                    <td>{{ monster.mapName || '未知地图' }}</td>
                                    <td>{{ monster.monsterId }}</td>
                                    <td>{{ monster.monsterName }}</td>
                                    <td>
                                        <span class="badge-info" v-if="monster.element">{{ monster.element }}</span>
                                        <span v-else>-</span>
                                    </td>
                                    <td>{{ monster.minLevel }}-{{ monster.maxLevel }}</td>
                                    <td>{{ monster.maxHp || 0 }}</td>
                                    <td>{{ monster.atk || 0 }}</td>
                                    <td>{{ monster.def || 0 }}</td>
                                    <td>{{ monster.exp || 0 }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-info me-1" 
                                                v-on:click="viewDetails(monster)" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning me-1" 
                                                v-on:click="showEditModal(monster)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" 
                                                v-on:click="deleteMapMonster(monster.id)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <nav aria-label="分页导航" v-if="pagination.totalPages > 1">
                        <ul class="pagination justify-content-center">
                            <li class="page-item" :class="{ disabled: pagination.page === 1 }">
                                <button class="page-link" v-on:click="changePage(pagination.page - 1)" :disabled="pagination.page === 1">
                                    上一页
                                </button>
                            </li>
                            <li class="page-item" v-for="page in getPageNumbers()" :key="page" 
                                :class="{ active: pagination.page === page }">
                                <button class="page-link" v-on:click="changePage(page)">{{ page }}</button>
                            </li>
                            <li class="page-item" :class="{ disabled: pagination.page === pagination.totalPages }">
                                <button class="page-link" v-on:click="changePage(pagination.page + 1)" 
                                        :disabled="pagination.page === pagination.totalPages">
                                    下一页
                                </button>
                            </li>
                        </ul>
                        <div class="text-center">
                            <small class="text-muted">
                                共 {{ pagination.totalCount }} 条记录，共 {{ pagination.totalPages }} 页，当前第 {{ pagination.page }} 页
                            </small>
                        </div>
                    </nav>
                </div>

                <!-- 新增地图怪物模态框 -->
            <div class="modal fade" id="createModal" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="createModalLabel">新增地图怪物</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="createForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMapId" class="form-label">地图 <span class="text-danger">*</span></label>
                                            <select class="form-control" v-model="createForm.mapId" id="createMapId" required>
                                                <option value="">请选择地图</option>
                                                <option v-for="map in maps" v-bind:key="map.mapId" v-bind:value="map.mapId">
                                                    {{ map.mapName }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMonsterSelect" class="form-label">选择怪物 <span class="text-danger">*</span></label>
                                            <select class="form-control" v-model="createForm.selectedMonsterId" 
                                                    id="createMonsterSelect" required v-on:change="onCreateMonsterChange">
                                                <option value="">请选择怪物</option>
                                                <option v-for="monster in monsterConfigs" v-bind:key="monster.id" v-bind:value="monster.id">
                                                    {{ monster.name }} (ID: {{ monster.id }})
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createElement" class="form-label">五行属性</label>
                                            <select class="form-control" v-model="createForm.element" id="createElement">
                                                <option value="">请选择五行</option>
                                                <option value="金">金</option>
                                                <option value="木">木</option>
                                                <option value="水">水</option>
                                                <option value="火">火</option>
                                                <option value="土">土</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMinLevel" class="form-label">最小等级</label>
                                            <input type="number" class="form-control" v-model="createForm.minLevel" 
                                                   id="createMinLevel" placeholder="最小等级" min="1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMaxLevel" class="form-label">最大等级</label>
                                            <input type="number" class="form-control" v-model="createForm.maxLevel" 
                                                   id="createMaxLevel" placeholder="最大等级" min="1">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createGrowth" class="form-label">怪物成长</label>
                                            <input type="number" class="form-control" v-model="createForm.growth" 
                                                   id="createGrowth" placeholder="怪物成长" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMaxDrop" class="form-label">最大掉落</label>
                                            <input type="number" class="form-control" v-model="createForm.maxDrop" 
                                                   id="createMaxDrop" placeholder="最大掉落" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createExp" class="form-label">经验值</label>
                                            <input type="number" class="form-control" v-model="createForm.exp" 
                                                   id="createExp" placeholder="经验值" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createHp" class="form-label">生命</label>
                                            <input type="number" class="form-control" v-model="createForm.hp" 
                                                   id="createHp" placeholder="生命值" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMp" class="form-label">魔法</label>
                                            <input type="number" class="form-control" v-model="createForm.mp" 
                                                   id="createMp" placeholder="魔法值" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMaxHp" class="form-label">最大生命</label>
                                            <input type="number" class="form-control" v-model="createForm.maxHp" 
                                                   id="createMaxHp" placeholder="最大生命值" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createMaxMp" class="form-label">最大魔法</label>
                                            <input type="number" class="form-control" v-model="createForm.maxMp" 
                                                   id="createMaxMp" placeholder="最大魔法值" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createAtk" class="form-label">攻击</label>
                                            <input type="number" class="form-control" v-model="createForm.atk" 
                                                   id="createAtk" placeholder="攻击力" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createDef" class="form-label">防御</label>
                                            <input type="number" class="form-control" v-model="createForm.def" 
                                                   id="createDef" placeholder="防御力" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createDodge" class="form-label">闪避</label>
                                            <input type="number" class="form-control" v-model="createForm.dodge" 
                                                   id="createDodge" placeholder="闪避值" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="createSpd" class="form-label">速度</label>
                                            <input type="number" class="form-control" v-model="createForm.spd" 
                                                   id="createSpd" placeholder="速度值" min="0">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="submitCreate" v-bind:disabled="submitting">
                                <span v-if="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑地图怪物模态框 -->
            <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="editModalLabel">编辑地图怪物</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMapId" class="form-label">地图 <span class="text-danger">*</span></label>
                                            <select class="form-control" v-model="editForm.mapId" id="editMapId" required>
                                                <option value="">请选择地图</option>
                                                <option v-for="map in maps" v-bind:key="map.mapId" v-bind:value="map.mapId">
                                                    {{ map.mapName }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMonsterSelect" class="form-label">选择怪物 <span class="text-danger">*</span></label>
                                            <select class="form-control" v-model="editForm.selectedMonsterId" 
                                                    id="editMonsterSelect" required v-on:change="onEditMonsterChange">
                                                <option value="">请选择怪物</option>
                                                <option v-for="monster in monsterConfigs" v-bind:key="monster.id" v-bind:value="monster.id">
                                                    {{ monster.name }} (ID: {{ monster.id }})
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editElement" class="form-label">五行属性</label>
                                            <select class="form-control" v-model="editForm.element" id="editElement">
                                                <option value="">请选择五行</option>
                                                <option value="金">金</option>
                                                <option value="木">木</option>
                                                <option value="水">水</option>
                                                <option value="火">火</option>
                                                <option value="土">土</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMinLevel" class="form-label">最小等级</label>
                                            <input type="number" class="form-control" v-model="editForm.minLevel" 
                                                   id="editMinLevel" placeholder="最小等级" min="1">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMaxLevel" class="form-label">最大等级</label>
                                            <input type="number" class="form-control" v-model="editForm.maxLevel" 
                                                   id="editMaxLevel" placeholder="最大等级" min="1">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editGrowth" class="form-label">怪物成长</label>
                                            <input type="number" class="form-control" v-model="editForm.growth" 
                                                   id="editGrowth" placeholder="怪物成长" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMaxDrop" class="form-label">最大掉落</label>
                                            <input type="number" class="form-control" v-model="editForm.maxDrop" 
                                                   id="editMaxDrop" placeholder="最大掉落" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editExp" class="form-label">经验值</label>
                                            <input type="number" class="form-control" v-model="editForm.exp" 
                                                   id="editExp" placeholder="经验值" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editHp" class="form-label">生命</label>
                                            <input type="number" class="form-control" v-model="editForm.hp" 
                                                   id="editHp" placeholder="生命值" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMp" class="form-label">魔法</label>
                                            <input type="number" class="form-control" v-model="editForm.mp" 
                                                   id="editMp" placeholder="魔法值" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMaxHp" class="form-label">最大生命</label>
                                            <input type="number" class="form-control" v-model="editForm.maxHp" 
                                                   id="editMaxHp" placeholder="最大生命值" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editMaxMp" class="form-label">最大魔法</label>
                                            <input type="number" class="form-control" v-model="editForm.maxMp" 
                                                   id="editMaxMp" placeholder="最大魔法值" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editAtk" class="form-label">攻击</label>
                                            <input type="number" class="form-control" v-model="editForm.atk" 
                                                   id="editAtk" placeholder="攻击力" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editDef" class="form-label">防御</label>
                                            <input type="number" class="form-control" v-model="editForm.def" 
                                                   id="editDef" placeholder="防御力" min="0">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editDodge" class="form-label">闪避</label>
                                            <input type="number" class="form-control" v-model="editForm.dodge" 
                                                   id="editDodge" placeholder="闪避值" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="editSpd" class="form-label">速度</label>
                                            <input type="number" class="form-control" v-model="editForm.spd" 
                                                   id="editSpd" placeholder="速度值" min="0">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="submitEdit" v-bind:disabled="submitting">
                                <span v-if="submitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 详情模态框 -->
            <div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="detailModalLabel">地图怪物详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row" v-if="selectedItem">
                                <div class="col-md-6 mb-2">
                                    <strong>地图ID:</strong> {{ selectedItem.mapId }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>怪物序号:</strong> {{ selectedItem.monsterId }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>怪物名称:</strong> {{ selectedItem.monsterName }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>怪物成长:</strong> {{ selectedItem.growth || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>五行属性:</strong> {{ selectedItem.element || '-' }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>等级范围:</strong> {{ selectedItem.minLevel }}-{{ selectedItem.maxLevel }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>最大掉落:</strong> {{ selectedItem.maxDrop || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>经验值:</strong> {{ selectedItem.exp || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>生命:</strong> {{ selectedItem.hp || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>魔法:</strong> {{ selectedItem.mp || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>最大生命:</strong> {{ selectedItem.maxHp || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>最大魔法:</strong> {{ selectedItem.maxMp || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>攻击:</strong> {{ selectedItem.atk || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>防御:</strong> {{ selectedItem.def || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>闪避:</strong> {{ selectedItem.dodge || 0 }}
                                </div>
                                <div class="col-md-6 mb-2">
                                    <strong>速度:</strong> {{ selectedItem.spd || 0 }}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </section>
</div>

@section Scripts {
    <script>
        Vue.createApp({
            data() {
                return {
                    mapMonsters: [],
                    maps: [],
                    monsterConfigs: [],
                    searchForm: {
                        MapId: '',
                        MonsterName: '',
                        Element: ''
                    },
                    pagination: {
                        page: 1,
                        pageSize: 10,
                        totalCount: 0,
                        totalPages: 0
                    },
                    loading: false,
                    submitting: false,
                    selectedItem: null,
                    createForm: {
                        mapId: '',
                        selectedMonsterId: '',
                        monsterId: '',
                        monsterName: '',
                        growth: 0,
                        element: '',
                        minLevel: 0,
                        maxLevel: 0,
                        maxDrop: 0,
                        exp: 0,
                        hp: 0,
                        mp: 0,
                        maxHp: 0,
                        maxMp: 0,
                        atk: 0,
                        def: 0,
                        dodge: 0,
                        spd: 0
                    },
                    editForm: {
                        id: '',
                        mapId: '',
                        selectedMonsterId: '',
                        monsterId: '',
                        monsterName: '',
                        growth: 0,
                        element: '',
                        minLevel: 0,
                        maxLevel: 0,
                        maxDrop: 0,
                        exp: 0,
                        hp: 0,
                        mp: 0,
                        maxHp: 0,
                        maxMp: 0,
                        atk: 0,
                        def: 0,
                        dodge: 0,
                        spd: 0
                    }
                };
            },
            async mounted() {
                try {
                    // 串行加载以避免并发连接问题
                    await this.loadMaps();
                    await this.loadMonsterConfigs();
                    await this.loadMapMonsters();
                    console.log('✅ 地图怪物管理页面初始化完成');
                } catch (error) {
                    console.error('❌ 页面初始化失败:', error);
                }
            },
            methods: {
                // 加载地图列表
                async loadMaps() {
                    // 防止重复调用
                    if (this._loadingMaps) {
                        console.log('🗺️ loadMaps已在执行中，跳过重复调用');
                        return;
                    }
                    this._loadingMaps = true;
                    
                    try {
                        const response = await fetch('/MapConfig/GetAll');
                        const result = await response.json();
                        if (result.success) {
                            this.maps = result.data || [];
                            console.log('✅ 成功加载地图列表，数量:', this.maps.length);
                        } else {
                            console.error('❌ 加载地图列表失败:', result.message);
                        }
                    } catch (error) {
                        console.error('❌ 加载地图列表异常：', error);
                    } finally {
                        this._loadingMaps = false;
                    }
                },

                // 加载怪物配置列表
                async loadMonsterConfigs() {
                    try {
                        console.log('开始加载怪物配置列表...');
                        const response = await fetch('/MonsterConfig/GetAll');
                        console.log('怪物配置API响应状态:', response.status);
                        const result = await response.json();
                        console.log('怪物配置API响应结果:', result);
                        if (result.success) {
                            this.monsterConfigs = result.data || [];
                            console.log('加载到的怪物配置数据:', this.monsterConfigs);
                        } else {
                            console.error('加载怪物配置失败:', result.message);
                            alert('加载怪物配置失败：' + result.message);
                        }
                    } catch (error) {
                        console.error('加载怪物配置列表失败：', error);
                        alert('加载怪物配置列表失败：' + error.message);
                    }
                },

                // 加载地图怪物列表
                async loadMapMonsters() {
                    this.loading = true;
                    try {
                        // 构建查询参数，过滤掉空值
                        const queryParams = {
                            Page: this.pagination.page,
                            PageSize: this.pagination.pageSize
                        };
                        
                        // 只添加非空的搜索条件
                        if (this.searchForm.MapId) {
                            queryParams.MapId = this.searchForm.MapId;
                        }
                        if (this.searchForm.MonsterName) {
                            queryParams.MonsterName = this.searchForm.MonsterName;
                        }
                        if (this.searchForm.Element) {
                            queryParams.Element = this.searchForm.Element;
                        }
                        
                        const params = new URLSearchParams(queryParams);
                        
                        const response = await fetch('/MapMonster/GetPagedList?' + params.toString());
                        const result = await response.json();
                        
                        if (result.success) {
                            this.mapMonsters = result.data || [];
                            this.pagination.totalCount = result.totalCount;
                            this.pagination.totalPages = result.totalPages;
                        } else {
                            alert('加载数据失败：' + result.message);
                        }
                    } catch (error) {
                        console.error('加载地图怪物列表失败：', error);
                        alert('加载数据失败');
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 搜索
                searchMapMonsters() {
                    this.pagination.page = 1;
                    this.loadMapMonsters();
                },
                
                // 重置搜索
                resetSearch() {
                    this.searchForm = {
                        MapId: '',
                        MonsterName: '',
                        Element: ''
                    };
                    this.pagination.page = 1;
                    this.loadMapMonsters();
                },
                
                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.pagination.totalPages) {
                        this.pagination.page = page;
                        this.loadMapMonsters();
                    }
                },
                
                // 获取页码数组
                getPageNumbers() {
                    const pages = [];
                    const start = Math.max(1, this.pagination.page - 2);
                    const end = Math.min(this.pagination.totalPages, start + 4);
                    
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                },
                
                // 新增时选择怪物
                onCreateMonsterChange() {
                    if (this.createForm.selectedMonsterId) {
                        const selectedMonster = this.monsterConfigs.find(m => m.id === this.createForm.selectedMonsterId);
                        if (selectedMonster) {
                            this.createForm.monsterId = selectedMonster.id;
                            this.createForm.monsterName = selectedMonster.name;
                            // 可以根据需要自动填充其他属性
                            this.createForm.element = selectedMonster.attribute || '';
                        }
                    } else {
                        this.createForm.monsterId = '';
                        this.createForm.monsterName = '';
                        this.createForm.element = '';
                    }
                },

                // 编辑时选择怪物
                onEditMonsterChange() {
                    if (this.editForm.selectedMonsterId) {
                        const selectedMonster = this.monsterConfigs.find(m => m.id === this.editForm.selectedMonsterId);
                        if (selectedMonster) {
                            this.editForm.monsterId = selectedMonster.id;
                            this.editForm.monsterName = selectedMonster.name;
                            // 可以根据需要自动填充其他属性
                            this.editForm.element = selectedMonster.attribute || '';
                        }
                    } else {
                        this.editForm.monsterId = '';
                        this.editForm.monsterName = '';
                        this.editForm.element = '';
                    }
                },

                // 显示新增模态框
                showCreateModal() {

                    // 重置表单
                    this.createForm = {
                        mapId: '',
                        selectedMonsterId: '',
                        monsterId: '',
                        monsterName: '',
                        growth: 0,
                        element: '',
                        minLevel: 0,
                        maxLevel: 0,
                        maxDrop: 0,
                        exp: 0,
                        hp: 0,
                        mp: 0,
                        maxHp: 0,
                        maxMp: 0,
                        atk: 0,
                        def: 0,
                        dodge: 0,
                        spd: 0
                    };
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('createModal'));
                    modal.show();
                },
                
                // 提交新增
                async submitCreate() {
                    if (!this.validateCreateForm()) {
                        return;
                    }
                    
                    this.submitting = true;
                    try {
                        const response = await fetch('/MapMonster/Create', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                            },
                            body: JSON.stringify(this.createForm)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            alert(result.message || '创建成功');
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('createModal'));
                            modal.hide();
                            // 刷新列表
                            this.loadMapMonsters();
                        } else {
                            alert(result.message || '创建失败');
                        }
                    } catch (error) {
                        console.error('创建地图怪物失败：', error);
                        alert('创建失败');
                    } finally {
                        this.submitting = false;
                    }
                },
                
                // 验证新增表单
                validateCreateForm() {
                    if (!this.createForm.mapId) {
                        alert('请选择地图');
                        return false;
                    }
                    if (!this.createForm.selectedMonsterId) {
                        alert('请选择怪物');
                        return false;
                    }
                    return true;
                },
                
                // 显示编辑模态框
                showEditModal(item) {
                    // 复制数据到编辑表单
                    this.editForm = {
                        id: item.id,
                        mapId: item.mapId,
                        selectedMonsterId: item.monsterId, // 设置选中的怪物ID
                        monsterId: item.monsterId,
                        monsterName: item.monsterName,
                        growth: item.growth || 0,
                        element: item.element || '',
                        minLevel: item.minLevel || 0,
                        maxLevel: item.maxLevel || 0,
                        maxDrop: item.maxDrop || 0,
                        exp: item.exp || 0,
                        hp: item.hp || 0,
                        mp: item.mp || 0,
                        maxHp: item.maxHp || 0,
                        maxMp: item.maxMp || 0,
                        atk: item.atk || 0,
                        def: item.def || 0,
                        dodge: item.dodge || 0,
                        spd: item.spd || 0
                    };
                    
                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('editModal'));
                    modal.show();
                },
                
                // 提交编辑
                async submitEdit() {
                    if (!this.validateEditForm()) {
                        return;
                    }
                    
                    this.submitting = true;
                    try {
                        const response = await fetch('/MapMonster/Update', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                            },
                            body: JSON.stringify(this.editForm)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            alert(result.message || '更新成功');
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                            modal.hide();
                            // 刷新列表
                            this.loadMapMonsters();
                        } else {
                            alert(result.message || '更新失败');
                        }
                    } catch (error) {
                        console.error('更新地图怪物失败：', error);
                        alert('更新失败');
                    } finally {
                        this.submitting = false;
                    }
                },
                
                // 验证编辑表单
                validateEditForm() {
                    if (!this.editForm.mapId) {
                        alert('请选择地图');
                        return false;
                    }
                    if (!this.editForm.selectedMonsterId) {
                        alert('请选择怪物');
                        return false;
                    }
                    return true;
                },
                
                // 查看详情
                viewDetails(item) {
                    this.selectedItem = item;
                    const modal = new bootstrap.Modal(document.getElementById('detailModal'));
                    modal.show();
                },
                
                // 删除地图怪物
                async deleteMapMonster(id) {
                    if (!confirm('确定要删除这个地图怪物吗？')) {
                        return;
                    }
                    
                    try {
                        const response = await fetch('/MapMonster/Delete', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                            },
                            body: JSON.stringify({ id })
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            alert(result.message || '删除成功');
                            this.loadMapMonsters();
                        } else {
                            alert(result.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除地图怪物失败：', error);
                        alert('删除失败');
                    }
                }
            }
        }).mount('#mapMonsterApp');
    </script>
}