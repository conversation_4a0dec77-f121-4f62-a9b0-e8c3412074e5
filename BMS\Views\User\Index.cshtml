@{
    ViewData["Title"] = "用户管理";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --card-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        --card-hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        --border-radius: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modern-page {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .modern-card:hover {
        box-shadow: var(--card-hover-shadow);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 1.5rem;
    }

    .modern-card-title {
        font-weight: 600;
        font-size: 1.25rem;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1em;
    }

    .modern-small-box {
        background: linear-gradient(135deg, var(--bg-start, #667eea), var(--bg-end, #764ba2));
        border-radius: var(--border-radius);
        padding: 2rem;
        color: white;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .modern-small-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .modern-small-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .modern-small-box.bg-info {
        --bg-start: #4facfe;
        --bg-end: #00f2fe;
    }

    .modern-small-box.bg-success {
        --bg-start: #11998e;
        --bg-end: #38ef7d;
    }

    .modern-small-box.bg-warning {
        --bg-start: #f093fb;
        --bg-end: #f5576c;
    }

    .modern-small-box.bg-danger {
        --bg-start: #fa709a;
        --bg-end: #fee140;
    }

    .modern-btn {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .modern-btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }

    .modern-btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .modern-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.3);
        color: white;
    }

    .modern-btn-warning {
        background: var(--warning-gradient);
        color: white;
    }

    .modern-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        color: white;
    }

    .modern-btn-info {
        background: var(--info-gradient);
        color: white;
    }

    .modern-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        color: white;
    }

    .modern-btn-danger {
        background: var(--danger-gradient);
        color: white;
    }

    .modern-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
        color: white;
    }

    .modern-form-control {
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
    }

    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        background: white;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: none;
        font-weight: 600;
        color: #475569;
        padding: 1rem;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.001);
    }

    .modern-table tbody td {
        padding: 1rem;
        border-color: #f1f5f9;
    }

    .modern-badge {
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        font-weight: 500;
        font-size: 0.75rem;
    }

    .modern-pagination {
        gap: 0.5rem;
    }

    .modern-pagination .page-link {
        border-radius: 10px;
        border: none;
        margin: 0 2px;
        padding: 0.6rem 1rem;
        transition: var(--transition);
    }

    .modern-pagination .page-item.active .page-link {
        background: var(--primary-gradient);
        border: none;
    }

    .modern-pagination .page-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .modern-breadcrumb {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 0;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }

    .fade-in-delay-1 {
        animation: fadeInUp 0.6s ease 0.1s forwards;
        opacity: 0;
    }

    .fade-in-delay-2 {
        animation: fadeInUp 0.6s ease 0.2s forwards;
        opacity: 0;
    }

    .fade-in-delay-3 {
        animation: fadeInUp 0.6s ease 0.3s forwards;
        opacity: 0;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-header h1 {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }
</style>

<div class="modern-page">
<div class="content-wrapper">
    <!-- 页面标题 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0 fade-in">
                        <i class="fas fa-users mr-2"></i>用户管理
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="modern-breadcrumb breadcrumb float-sm-right fade-in-delay-1">
                        <li class="breadcrumb-item"><a href="/" style="color: #667eea; text-decoration: none;">首页</a></li>
                        <li class="breadcrumb-item">用户管理</li>
                        <li class="breadcrumb-item active">用户信息管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <section class="content">
        <div class="container-fluid" id="userApp">
            <!-- 统计卡片 -->
            <div class="row" v-if="showStatistics">
                <div class="col-lg-3 col-6 fade-in">
                    <div class="modern-small-box bg-info">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ statistics.totalUsers || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">用户总数</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6 fade-in-delay-1">
                    <div class="modern-small-box bg-success">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ statistics.todayNewUsers || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">今日新增</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6 fade-in-delay-2">
                    <div class="modern-small-box bg-warning">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ statistics.vipUsers || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">VIP用户</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-crown"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6 fade-in-delay-3">
                    <div class="modern-small-box bg-danger">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ formatNumber(statistics.totalGold) || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">金币总量</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 查询表单 -->
            <div class="modern-card fade-in-delay-1">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-search"></i> 查询条件
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="modern-btn modern-btn-info btn-sm" v-on:click="toggleStatistics">
                            <i class="fas fa-chart-bar"></i> {{ showStatistics ? '隐藏' : '显示' }}统计
                        </button>
                    </div>
                </div>
                <div class="card-body" style="padding: 2rem;">
                    <div class="row">
                        <div class="col-md-2">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">用户ID：</label>
                            <input type="number" class="modern-form-control" v-model="queryForm.userId" placeholder="请输入用户ID">
                        </div>
                        <div class="col-md-3">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">用户名：</label>
                            <input type="text" class="modern-form-control" v-model="queryForm.username" placeholder="请输入用户名">
                        </div>
                        <div class="col-md-3">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">昵称：</label>
                            <input type="text" class="modern-form-control" v-model="queryForm.nickname" placeholder="请输入昵称">
                        </div>
                        <div class="col-md-2">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">性别：</label>
                            <select class="modern-form-control" v-model="queryForm.sex">
                                <option value="">全部</option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">VIP等级：</label>
                            <select class="modern-form-control" v-model="queryForm.vipLevel">
                                <option value="">全部</option>
                                <option value="0">普通用户</option>
                                <option value="1">VIP1</option>
                                <option value="2">VIP2</option>
                                <option value="3">VIP3</option>
                                <option value="4">VIP4</option>
                                <option value="5">VIP5</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-12 text-right">
                            <button type="button" class="modern-btn modern-btn-info me-2" v-on:click="loadData">
                                <i class="fas fa-search"></i> 查询
                            </button>
                            <button type="button" class="modern-btn modern-btn-secondary me-2" v-on:click="resetQuery">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                            <button type="button" class="modern-btn modern-btn-primary" v-on:click="showCreateModal">
                                <i class="fas fa-plus"></i> 新增用户
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 用户列表 -->
            <div class="modern-card fade-in-delay-2">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-list"></i> 用户列表
                    </h3>
                    <div class="card-tools">
                        <span class="modern-badge badge-info" style="background: var(--info-gradient); color: white; border-radius: 12px; padding: 0.5rem 1rem;">
                            共 {{ totalCount }} 条记录
                        </span>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="modern-table table text-nowrap" style="margin: 0;">
                        <thead>
                            <tr>
                                <th>用户ID</th>
                                <th>用户名</th>
                                <th>昵称</th>
                                <th>性别</th>
                                <th>VIP等级</th>
                                <th>金币</th>
                                <th>元宝</th>
                                <th>水晶</th>
                                <th>注册时间</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in userList" v-bind:key="item.id">
                                <td><span style="font-weight: 600; color: #667eea;">{{ item.id }}</span></td>
                                <td><strong>{{ item.username }}</strong></td>
                                <td>{{ item.nickname || '-' }}</td>
                                <td>{{ item.sexText }}</td>
                                <td><span class="modern-badge" v-bind:class="item.vipLevel > 0 ? 'badge-warning' : 'badge-secondary'" style="border-radius: 8px;">{{ item.vipLevelText }}</span></td>
                                <td><span style="color: #f59e0b; font-weight: 500;">{{ formatNumber(item.gold) }}</span></td>
                                <td><span style="color: #8b5cf6; font-weight: 500;">{{ formatNumber(item.yuanbao) }}</span></td>
                                <td><span style="color: #06b6d4; font-weight: 500;">{{ formatNumber(item.crystal) }}</span></td>
                                <td style="color: #6b7280;">{{ formatDate(item.regTime) }}</td>
                                <td>
                                    <div style="display: flex; gap: 0.25rem;">
                                        <button type="button" class="modern-btn modern-btn-warning btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="showEditModal(item)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-success btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="showAssetModal(item)" title="资产管理">
                                            <i class="fas fa-coins"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-info btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="showResetPasswordModal(item)" title="重置密码">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="deleteUser(item)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="card-footer" v-if="totalPages > 1" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 0 0 var(--border-radius) var(--border-radius); border-top: 1px solid rgba(0, 0, 0, 0.05); padding: 1.5rem;">
                    <div class="row align-items-center">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info" style="color: #64748b; font-weight: 500;">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers float-right">
                                <ul class="modern-pagination pagination">
                                    <li class="paginate_button page-item previous" v-bind:class="{disabled: currentPage === 1}">
                                        <a href="#" class="page-link" v-on:click="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="paginate_button page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{active: page === currentPage}">
                                        <a href="#" class="page-link" v-on:click="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="paginate_button page-item next" v-bind:class="{disabled: currentPage === totalPages}">
                                        <a href="#" class="page-link" v-on:click="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增用户模态框 -->
            <div class="modal fade" id="createModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">新增用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>用户名</label>
                                        <input type="text" class="form-control" v-model="createForm.username" placeholder="请输入用户名">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>密码</label>
                                        <input type="password" class="form-control" v-model="createForm.password" placeholder="请输入密码">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>昵称</label>
                                        <input type="text" class="form-control" v-model="createForm.nickname" placeholder="请输入昵称">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>性别</label>
                                        <select class="form-control" v-model="createForm.sex">
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="createUser" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑用户模态框 -->
            <div class="modal fade" id="editModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑用户</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>用户ID</label>
                                        <input type="text" class="form-control" v-model="editForm.id" readonly>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>用户名</label>
                                        <input type="text" class="form-control" v-model="editForm.username" placeholder="请输入用户名">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>昵称</label>
                                        <input type="text" class="form-control" v-model="editForm.nickname" placeholder="请输入昵称">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>性别</label>
                                        <select class="form-control" v-model="editForm.sex">
                                            <option value="">请选择性别</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="updateUser" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 资产管理模态框 -->
            <div class="modal fade" id="assetModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">资产管理</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>用户信息</label>
                                <input type="text" class="form-control" v-bind:value="assetForm.username + ' (ID: ' + assetForm.id + ')'" readonly>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>金币</label>
                                        <input type="number" class="form-control" v-model="assetForm.gold" placeholder="请输入金币" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>元宝</label>
                                        <input type="number" class="form-control" v-model="assetForm.yuanbao" placeholder="请输入元宝" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>水晶</label>
                                        <input type="number" class="form-control" v-model="assetForm.crystal" placeholder="请输入水晶" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>VIP等级</label>
                                        <select class="form-control" v-model="assetForm.vipLevel">
                                            <option value="0">普通用户</option>
                                            <option value="1">VIP1</option>
                                            <option value="2">VIP2</option>
                                            <option value="3">VIP3</option>
                                            <option value="4">VIP4</option>
                                            <option value="5">VIP5</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>VIP积分</label>
                                        <input type="number" class="form-control" v-model="assetForm.vipScore" placeholder="请输入VIP积分" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="updateAsset" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 重置密码模态框 -->
            <div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">重置密码</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>用户信息</label>
                                <input type="text" class="form-control" v-bind:value="resetPasswordForm.username + ' (ID: ' + resetPasswordForm.id + ')'" readonly>
                            </div>
                            <div class="form-group">
                                <label>新密码</label>
                                <input type="password" class="form-control" v-model="resetPasswordForm.newPassword" placeholder="请输入新密码">
                            </div>
                            <div class="form-group">
                                <label>确认密码</label>
                                <input type="password" class="form-control" v-model="resetPasswordForm.confirmPassword" placeholder="请确认新密码">
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="resetPassword" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
</div>

<!-- 引入Vue.js和axios -->
<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>

<script>
    const { createApp } = Vue;

    createApp({
        data() {
            return {
                // 查询条件
                queryForm: {
                    userId: '',
                    username: '',
                    nickname: '',
                    sex: '',
                    vipLevel: '',
                    page: 1,
                    pageSize: 15
                },
                // 用户列表
                userList: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: 1,
                pageSize: 15,
                submitting: false,
                // 统计信息
                showStatistics: true,
                statistics: {},
                // 新增表单
                createForm: {
                    username: '',
                    password: '',
                    nickname: '',
                    sex: '',
                    vipLevel: 0,
                    vipScore: 0,
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    title: ''
                },
                // 编辑表单
                editForm: {
                    id: '',
                    username: '',
                    nickname: '',
                    sex: '',
                    vipLevel: 0,
                    vipScore: 0,
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    title: '',
                    mainPetName: ''
                },
                // 资产管理表单
                assetForm: {
                    id: '',
                    username: '',
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    vipLevel: 0,
                    vipScore: 0
                },
                // 重置密码表单
                resetPasswordForm: {
                    id: '',
                    username: '',
                    newPassword: '',
                    confirmPassword: ''
                }
            };
        },
        computed: {
            // 计算可见的页码
            visiblePages() {
                const pages = [];
                const total = this.totalPages;
                const current = this.currentPage;
                
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                if (end - start < 4) {
                    if (start === 1) {
                        end = Math.min(total, start + 4);
                    } else {
                        start = Math.max(1, end - 4);
                    }
                }
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            }
        },
        async mounted() {
            await this.loadStatistics();
            await this.loadData();
        },
        methods: {
            // 加载统计信息
            async loadStatistics() {
                try {
                    const response = await axios.get('/User/GetStatistics');
                    if (response.data.success) {
                        this.statistics = response.data.data || {};
                    }
                } catch (error) {
                    console.error('加载统计信息失败：', error);
                }
            },
            
            // 切换统计显示
            toggleStatistics() {
                this.showStatistics = !this.showStatistics;
                if (this.showStatistics) {
                    this.loadStatistics();
                }
            },

            // 加载数据
            async loadData() {
                try {
                    const response = await axios.post('/User/GetList', {
                        userId: this.queryForm.userId || null,
                        username: this.queryForm.username || null,
                        nickname: this.queryForm.nickname || null,
                        sex: this.queryForm.sex || null,
                        vipLevel: this.queryForm.vipLevel ? parseInt(this.queryForm.vipLevel) : null,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        const result = response.data.data || {};
                        this.userList = result.data || [];
                        this.totalCount = result.totalCount || 0;
                        this.totalPages = result.totalPages || 0;
                        this.currentPage = result.pageIndex || 1;
                    } else {
                        alert(response.data.message || '加载数据失败');
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                }
            },

            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    userId: '',
                    username: '',
                    nickname: '',
                    sex: '',
                    vipLevel: '',
                    page: 1,
                    pageSize: 15
                };
                this.currentPage = 1;
                this.loadData();
            },

            // 换页
            changePage(page) {
                if (page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            },

            // 显示新增模态框
            showCreateModal() {
                this.createForm = {
                    username: '',
                    password: '',
                    nickname: '',
                    sex: '',
                    vipLevel: 0,
                    vipScore: 0,
                    gold: 0,
                    yuanbao: 0,
                    crystal: 0,
                    title: ''
                };
                new bootstrap.Modal(document.getElementById('createModal')).show();
            },

            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    username: item.username,
                    nickname: item.nickname || '',
                    sex: item.sex || '',
                    vipLevel: item.vipLevel || 0,
                    vipScore: item.vipScore || 0,
                    gold: item.gold || 0,
                    yuanbao: item.yuanbao || 0,
                    crystal: item.crystal || 0,
                    title: item.title || '',
                    mainPetName: item.mainPetName || ''
                };
                new bootstrap.Modal(document.getElementById('editModal')).show();
            },

            // 显示资产管理模态框
            showAssetModal(item) {
                this.assetForm = {
                    id: item.id,
                    username: item.username,
                    gold: item.gold || 0,
                    yuanbao: item.yuanbao || 0,
                    crystal: item.crystal || 0,
                    vipLevel: item.vipLevel || 0,
                    vipScore: item.vipScore || 0
                };
                new bootstrap.Modal(document.getElementById('assetModal')).show();
            },

            // 显示重置密码模态框
            showResetPasswordModal(item) {
                this.resetPasswordForm = {
                    id: item.id,
                    username: item.username,
                    newPassword: '',
                    confirmPassword: ''
                };
                new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
            },

            // 创建用户
            async createUser() {
                if (!this.validateCreateForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/Create', this.createForm);
                    
                    if (response.data.success) {
                        alert('用户创建成功');
                        bootstrap.Modal.getInstance(document.getElementById('createModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '创建失败');
                    }
                } catch (error) {
                    console.error('创建用户失败：', error);
                    alert('创建用户失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 更新用户
            async updateUser() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert('用户更新成功');
                        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                        this.loadData();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新用户失败：', error);
                    alert('更新用户失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 更新资产
            async updateAsset() {
                if (!this.validateAssetForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/UpdateAsset', this.assetForm);
                    
                    if (response.data.success) {
                        alert('资产更新成功');
                        bootstrap.Modal.getInstance(document.getElementById('assetModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新资产失败：', error);
                    alert('更新资产失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 重置密码
            async resetPassword() {
                if (!this.validateResetPasswordForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/User/ResetPassword', {
                        id: this.resetPasswordForm.id,
                        newPassword: this.resetPasswordForm.newPassword
                    });
                    
                    if (response.data.success) {
                        alert('密码重置成功');
                        bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
                    } else {
                        alert(response.data.message || '重置失败');
                    }
                } catch (error) {
                    console.error('重置密码失败：', error);
                    alert('重置密码失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 删除用户
            async deleteUser(item) {
                if (!confirm(`确定要删除用户 "${item.username}" 吗？\n\n警告：此操作将删除用户的所有相关数据，且无法恢复！`)) {
                    return;
                }

                try {
                    const response = await axios.post('/User/Delete', { id: item.id });
                    
                    if (response.data.success) {
                        alert('用户删除成功');
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除用户失败：', error);
                    alert('删除用户失败，请重试');
                }
            },

            // 验证新增表单
            validateCreateForm() {
                if (!this.createForm.username?.trim()) {
                    alert('请输入用户名');
                    return false;
                }
                if (!this.createForm.password?.trim()) {
                    alert('请输入密码');
                    return false;
                }
                if (this.createForm.password.length < 6) {
                    alert('密码长度不能少于6位');
                    return false;
                }
                return true;
            },

            // 验证编辑表单
            validateEditForm() {
                if (!this.editForm.username?.trim()) {
                    alert('请输入用户名');
                    return false;
                }
                return true;
            },

            // 验证资产表单
            validateAssetForm() {
                if (this.assetForm.gold < 0) {
                    alert('金币不能为负数');
                    return false;
                }
                if (this.assetForm.yuanbao < 0) {
                    alert('元宝不能为负数');
                    return false;
                }
                if (this.assetForm.crystal < 0) {
                    alert('水晶不能为负数');
                    return false;
                }
                return true;
            },

            // 验证重置密码表单
            validateResetPasswordForm() {
                if (!this.resetPasswordForm.newPassword?.trim()) {
                    alert('请输入新密码');
                    return false;
                }
                if (this.resetPasswordForm.newPassword.length < 6) {
                    alert('密码长度不能少于6位');
                    return false;
                }
                if (this.resetPasswordForm.newPassword !== this.resetPasswordForm.confirmPassword) {
                    alert('两次输入的密码不一致');
                    return false;
                }
                return true;
            },

            // 格式化数字
            formatNumber(num) {
                if (num == null) return '0';
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            },

            // 格式化日期
            formatDate(dateStr) {
                if (!dateStr) return '-';
                try {
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    });
                } catch {
                    return dateStr;
                }
            }
        }
    }).mount('#userApp');
</script>