using System.ComponentModel.DataAnnotations;

namespace BMS.Models.DTOs
{
    /// <summary>
    /// 任务配置显示DTO
    /// </summary>
    public class TaskConfigDto
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = string.Empty;

        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string? TaskDescription { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public int TaskType { get; set; }

        /// <summary>
        /// 任务类型名称
        /// </summary>
        public string TaskTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool IsRepeatable { get; set; }

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string? PrerequisiteTask { get; set; }

        /// <summary>
        /// 前置任务名称
        /// </summary>
        public string? PrerequisiteTaskName { get; set; }

        /// <summary>
        /// 指定宠物ID
        /// </summary>
        public string? RequiredPet { get; set; }

        /// <summary>
        /// 奖励配置
        /// </summary>
        public string? RewardConfig { get; set; }

        /// <summary>
        /// 是否网络任务
        /// </summary>
        public bool IsNetworkTask { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// 任务目标列表
        /// </summary>
        public List<TaskObjectiveDto>? Objectives { get; set; }
    }

    /// <summary>
    /// 任务配置查询DTO
    /// </summary>
    public class TaskConfigQueryDto : PagedQueryDto
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string? TaskName { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        public int? TaskType { get; set; }

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool? IsRepeatable { get; set; }
    }

    /// <summary>
    /// 任务配置创建DTO
    /// </summary>
    public class TaskConfigCreateDto
    {
        /// <summary>
        /// 任务ID（创建时自动生成，更新时必填）
        /// </summary>
        [StringLength(50, ErrorMessage = "任务ID长度不能超过50个字符")]
        public string? TaskId { get; set; }

        /// <summary>
        /// 任务名称
        /// </summary>
        [Required(ErrorMessage = "任务名称不能为空")]
        [StringLength(200, ErrorMessage = "任务名称长度不能超过200个字符")]
        public string TaskName { get; set; } = string.Empty;

        /// <summary>
        /// 任务描述
        /// </summary>
        public string? TaskDescription { get; set; }

        /// <summary>
        /// 任务类型
        /// </summary>
        [Range(0, 2, ErrorMessage = "任务类型必须在0-2之间")]
        public int TaskType { get; set; } = 0;

        /// <summary>
        /// 是否可重复
        /// </summary>
        public bool IsRepeatable { get; set; } = false;

        /// <summary>
        /// 前置任务ID
        /// </summary>
        public string? PrerequisiteTask { get; set; }

        /// <summary>
        /// 指定宠物ID
        /// </summary>
        public string? RequiredPet { get; set; }

        /// <summary>
        /// 奖励配置
        /// </summary>
        public string? RewardConfig { get; set; }

        /// <summary>
        /// 是否网络任务
        /// </summary>
        public bool IsNetworkTask { get; set; } = false;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 任务目标列表
        /// </summary>
        public List<TaskObjectiveCreateDto>? Objectives { get; set; }
    }

    /// <summary>
    /// 任务目标DTO
    /// </summary>
    public class TaskObjectiveDto
    {
        /// <summary>
        /// 目标ID
        /// </summary>
        public int ObjectiveId { get; set; }

        /// <summary>
        /// 任务ID
        /// </summary>
        public string TaskId { get; set; } = string.Empty;

        /// <summary>
        /// 目标类型
        /// </summary>
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// 目标类型名称
        /// </summary>
        public string ObjectiveTypeName { get; set; } = string.Empty;

        /// <summary>
        /// 目标ID
        /// </summary>
        public string? TargetId { get; set; }

        /// <summary>
        /// 目标名称
        /// </summary>
        public string? TargetName { get; set; }

        /// <summary>
        /// 目标数量
        /// </summary>
        public int TargetAmount { get; set; }

        /// <summary>
        /// 目标顺序
        /// </summary>
        public int ObjectiveOrder { get; set; }

        /// <summary>
        /// 目标描述
        /// </summary>
        public string? ObjectiveDescription { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime? CreatedAt { get; set; }
    }

    /// <summary>
    /// 任务目标创建DTO
    /// </summary>
    public class TaskObjectiveCreateDto
    {
        /// <summary>
        /// 目标类型
        /// </summary>
        [Required(ErrorMessage = "目标类型不能为空")]
        public string ObjectiveType { get; set; } = string.Empty;

        /// <summary>
        /// 目标ID
        /// </summary>
        public string? TargetId { get; set; }

        /// <summary>
        /// 目标数量
        /// </summary>
        [Range(1, int.MaxValue, ErrorMessage = "目标数量必须大于0")]
        public int TargetAmount { get; set; } = 1;

        /// <summary>
        /// 目标顺序
        /// </summary>
        public int ObjectiveOrder { get; set; } = 0;

        /// <summary>
        /// 目标描述
        /// </summary>
        public string? ObjectiveDescription { get; set; }
    }
}
