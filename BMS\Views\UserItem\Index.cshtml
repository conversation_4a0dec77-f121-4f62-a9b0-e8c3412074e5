@{
    ViewData["Title"] = "用户道具管理";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --card-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        --card-hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        --border-radius: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modern-page {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .modern-card:hover {
        box-shadow: var(--card-hover-shadow);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 1.5rem;
    }

    .modern-card-title {
        font-weight: 600;
        font-size: 1.25rem;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1em;
    }

    .modern-small-box {
        background: linear-gradient(135deg, var(--bg-start, #667eea), var(--bg-end, #764ba2));
        border-radius: var(--border-radius);
        padding: 2rem;
        color: white;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .modern-small-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        pointer-events: none;
    }

    .modern-small-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .modern-small-box.bg-info {
        --bg-start: #4facfe;
        --bg-end: #00f2fe;
    }

    .modern-small-box.bg-success {
        --bg-start: #11998e;
        --bg-end: #38ef7d;
    }

    .modern-small-box.bg-warning {
        --bg-start: #f093fb;
        --bg-end: #f5576c;
    }

    .modern-small-box.bg-danger {
        --bg-start: #fa709a;
        --bg-end: #fee140;
    }

    .modern-btn {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .modern-btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }

    .modern-btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .modern-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.3);
        color: white;
    }

    .modern-btn-warning {
        background: var(--warning-gradient);
        color: white;
    }

    .modern-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        color: white;
    }

    .modern-btn-info {
        background: var(--info-gradient);
        color: white;
    }

    .modern-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        color: white;
    }

    .modern-btn-danger {
        background: var(--danger-gradient);
        color: white;
    }

    .modern-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
        color: white;
    }

    .modern-form-control {
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
    }

    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        background: white;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: none;
        font-weight: 600;
        color: #475569;
        padding: 1rem;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.001);
    }

    .modern-table tbody td {
        padding: 1rem;
        border-color: #f1f5f9;
    }

    .modern-pagination {
        gap: 0.5rem;
    }

    .modern-pagination .page-link {
        border-radius: 10px;
        border: none;
        margin: 0 2px;
        padding: 0.6rem 1rem;
        transition: var(--transition);
    }

    .modern-pagination .page-item.active .page-link {
        background: var(--primary-gradient);
        border: none;
    }

    .modern-pagination .page-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .modern-breadcrumb {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 0;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }

    .fade-in-delay-1 {
        animation: fadeInUp 0.6s ease 0.1s forwards;
        opacity: 0;
    }

    .fade-in-delay-2 {
        animation: fadeInUp 0.6s ease 0.2s forwards;
        opacity: 0;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-header h1 {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    /* 自定义搜索下拉框样式 */
    .custom-select-container {
        position: relative;
        width: 100%;
    }

    .custom-select-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.9);
        transition: var(--transition);
        cursor: pointer;
    }

    .custom-select-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .custom-select-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 2px solid #667eea;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .custom-select-dropdown.show {
        display: block;
    }

    .custom-select-search {
        padding: 0.75rem;
        border: none;
        border-bottom: 1px solid #e2e8f0;
        width: 100%;
        border-radius: 8px 8px 0 0;
    }

    .custom-select-search:focus {
        outline: none;
        border-color: #667eea;
    }

    .custom-select-option {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f5f9;
        transition: var(--transition);
    }

    .custom-select-option:hover {
        background: rgba(102, 126, 234, 0.1);
    }

    .custom-select-option.selected {
        background: var(--primary-gradient);
        color: white;
    }

    .custom-select-option .item-name {
        font-weight: 600;
        color: #1e293b;
    }

    .custom-select-option .item-details {
        font-size: 0.875rem;
        color: #64748b;
        margin-top: 0.25rem;
    }

    .custom-select-option.selected .item-name,
    .custom-select-option.selected .item-details {
        color: white;
    }

    .custom-select-arrow {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        transition: var(--transition);
    }

    .custom-select-container.open .custom-select-arrow {
        transform: translateY(-50%) rotate(180deg);
    }
</style>

<div class="modern-page">

<div class="content-wrapper">
    <!-- 页面标题 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0 fade-in">
                        <i class="fas fa-box mr-2"></i>用户道具管理
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="modern-breadcrumb breadcrumb float-sm-right fade-in-delay-1">
                        <li class="breadcrumb-item"><a href="/" style="color: #667eea; text-decoration: none;">首页</a></li>
                        <li class="breadcrumb-item">用户管理</li>
                        <li class="breadcrumb-item active">用户道具管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <section class="content">
        <div class="container-fluid" id="userItemApp">
            <!-- 统计卡片 -->
            <div class="row" v-if="showStatistics">
                <div class="col-lg-3 col-6 fade-in">
                    <div class="modern-small-box bg-info">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ formatNumber(statistics.totalItems) || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">道具总数</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-boxes"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6 fade-in-delay-1">
                    <div class="modern-small-box bg-success">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ statistics.itemTypes || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">道具种类</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-layer-group"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6 fade-in-delay-2">
                    <div class="modern-small-box bg-warning">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ formatNumber(statistics.totalValue) || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">道具总价值</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6 fade-in-delay-3">
                    <div class="modern-small-box bg-danger">
                        <div class="inner">
                            <h3 style="font-size: 2.5rem; font-weight: 700; margin-bottom: 0.5rem;">{{ formatNumber(statistics.orangeItems + statistics.purpleItems) || 0 }}</h3>
                            <p style="font-size: 1rem; opacity: 0.9; margin: 0;">稀有道具</p>
                        </div>
                        <div class="icon" style="position: absolute; top: 1.5rem; right: 1.5rem; font-size: 2.5rem; opacity: 0.3;">
                            <i class="fas fa-gem"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 查询表单 -->
            <div class="modern-card fade-in-delay-1">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-search"></i> 查询条件
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="modern-btn modern-btn-info btn-sm" v-on:click="toggleStatistics">
                            <i class="fas fa-chart-bar"></i> {{ showStatistics ? '隐藏' : '显示' }}统计
                        </button>
                    </div>
                </div>
                <div class="card-body" style="padding: 2rem;">
                    <div class="row">
                        <div class="col-md-3">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">用户：</label>
                            <select class="modern-form-control" v-model="queryForm.userId">
                                <option value="">全部用户</option>
                                <option v-for="user in userOptions" v-bind:key="user.value" v-bind:value="user.value">
                                    {{ user.label }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">道具名称：</label>
                            <input type="text" class="modern-form-control" v-model="queryForm.itemName" placeholder="请输入道具名称">
                        </div>
                        <div class="col-md-2">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">道具类型：</label>
                            <select class="modern-form-control" v-model="queryForm.itemType">
                                <option value="">全部</option>
                                <option value="weapon">武器</option>
                                <option value="armor">装备</option>
                                <option value="consumable">消耗品</option>
                                <option value="material">材料</option>
                                <option value="quest">任务物品</option>
                                <option value="other">其它</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label style="font-weight: 500; color: #374151; margin-bottom: 0.5rem;">道具品质：</label>
                            <select class="modern-form-control" v-model="queryForm.itemQuality">
                                <option value="">全部</option>
                                <option value="white">普通</option>
                                <option value="green">精良</option>
                                <option value="blue">稀有</option>
                                <option value="purple">史诗</option>
                                <option value="orange">传说</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-12 text-right">
                            <button type="button" class="modern-btn modern-btn-info me-2" v-on:click="loadData">
                                <i class="fas fa-search"></i> 查询
                            </button>
                            <button type="button" class="modern-btn modern-btn-secondary me-2" v-on:click="resetQuery">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                            <button type="button" class="modern-btn modern-btn-primary" v-on:click="showAddModal">
                                <i class="fas fa-plus"></i> 添加道具
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 道具列表 -->
            <div class="modern-card fade-in-delay-2">
                <div class="modern-card-header">
                    <h3 class="modern-card-title">
                        <i class="fas fa-list"></i> 道具列表
                    </h3>
                    <div class="card-tools">
                        <span class="modern-badge badge-info" style="background: var(--info-gradient); color: white; border-radius: 12px; padding: 0.5rem 1rem;">
                            共 {{ totalCount }} 条记录
                        </span>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="modern-table table text-nowrap" style="margin: 0;">
                        <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>用户信息</th>
                                <th>道具信息</th>
                                <th>道具品质</th>
                                <th>道具类型</th>
                                <th>数量</th>
                                <th>位置</th>
                                <th>序号</th>
                                <th>单价</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in itemList" v-bind:key="item.id">
                                <td><span style="font-weight: 600; color: #667eea;">{{ item.id }}</span></td>
                                <td>
                                    <div><strong>{{ item.username }}</strong></div>
                                    <small class="text-muted">ID: {{ item.userId }}</small>
                                </td>
                                <td>
                                    <div><strong>{{ item.itemName }}</strong></div>
                                    <small class="text-muted">编号: {{ item.itemNo }}</small>
                                </td>
                                <td>
                                    <span class="badge" v-bind:class="item.qualityCssClass" style="border-radius: 8px;">{{ item.qualityText }}</span>
                                </td>
                                <td>{{ getTypeText(item.itemType) }}</td>
                                <td><strong style="color: #059669;">{{ formatNumber(item.itemCount) }}</strong></td>
                                <td style="color: #6b7280;">{{ item.itemPos || '-' }}</td>
                                <td style="color: #6b7280;">{{ item.itemSeq }}</td>
                                <td><span style="color: #f59e0b; font-weight: 500;">{{ item.itemPrice || 0 }}</span></td>
                                <td>
                                    <div style="display: flex; gap: 0.25rem;">
                                        <button type="button" class="modern-btn modern-btn-info btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="showDetailModal(item)" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-warning btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="showEditModal(item)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger btn-sm" style="padding: 0.375rem 0.75rem;" v-on:click="deleteItem(item)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <div class="card-footer" v-if="totalPages > 1" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 0 0 var(--border-radius) var(--border-radius); border-top: 1px solid rgba(0, 0, 0, 0.05); padding: 1.5rem;">
                    <div class="row align-items-center">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info" style="color: #64748b; font-weight: 500;">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers float-right">
                                <ul class="modern-pagination pagination">
                                    <li class="paginate_button page-item previous" v-bind:class="{disabled: currentPage === 1}">
                                        <a href="#" class="page-link" v-on:click="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="paginate_button page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{active: page === currentPage}">
                                        <a href="#" class="page-link" v-on:click="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="paginate_button page-item next" v-bind:class="{disabled: currentPage === totalPages}">
                                        <a href="#" class="page-link" v-on:click="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>            <!-- 添加道具模态框 -->
            <div class="modal fade" id="addModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">添加道具</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>用户</label>
                                        <select class="form-control" v-model="addForm.userId">
                                            <option value="">请选择用户</option>
                                            <option v-for="user in userOptions" v-bind:key="user.value" v-bind:value="user.value">
                                                {{ user.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>道具</label>
                                        <div class="custom-select-container" v-bind:class="{ open: showItemDropdown }">
                                            <input 
                                                type="text" 
                                                class="custom-select-input" 
                                                v-bind:value="selectedItemDisplay" 
                                                v-on:click="toggleItemDropdown"
                                                placeholder="请选择道具"
                                                readonly
                                            >
                                            <i class="fas fa-chevron-down custom-select-arrow"></i>
                                            <div class="custom-select-dropdown" v-bind:class="{ show: showItemDropdown }">
                                                <input 
                                                    type="text" 
                                                    class="custom-select-search" 
                                                    v-model="itemSearchText"
                                                    placeholder="搜索道具名称或编号..."
                                                    v-on:input="filterItems"
                                                >
                                                <div 
                                                    v-for="item in filteredItems" 
                                                    v-bind:key="item.itemNo"
                                                    class="custom-select-option"
                                                    v-bind:class="{ selected: addForm.itemNo == item.itemNo }"
                                                    v-on:click="selectItem(item)"
                                                >
                                                    <div class="item-name">{{ item.name }} ({{ item.itemNo }})</div>
                                                    <div class="item-details">
                                                        {{ getTypeText(item.type) }} | {{ getQualityText(item.quality) }} | 价格: {{ item.price || 0 }}
                                                    </div>
                                                </div>
                                                <div v-if="filteredItems.length === 0" class="custom-select-option" style="color: #64748b; text-align: center;">
                                                    未找到匹配的道具
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>数量</label>
                                        <input type="number" class="form-control" v-model="addForm.itemCount" placeholder="请输入数量" min="1">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>位置</label>
                                        <input type="number" class="form-control" v-model="addForm.itemPos" placeholder="道具位置（可选）">
                                    </div>
                                </div>
                            </div>
                            <!-- 道具预览 -->
                            <div class="row" v-if="selectedItemConfig">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> 道具信息</h6>
                                        <p><strong>名称：</strong>{{ selectedItemConfig.name }}</p>
                                        <p><strong>类型：</strong>{{ getTypeText(selectedItemConfig.type) }}</p>
                                        <p><strong>品质：</strong><span class="badge" v-bind:class="getQualityCssClass(selectedItemConfig.quality)">{{ getQualityText(selectedItemConfig.quality) }}</span></p>
                                        <p><strong>价格：</strong>{{ selectedItemConfig.price || 0 }}</p>
                                        <p v-if="selectedItemConfig.description"><strong>描述：</strong>{{ selectedItemConfig.description }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="addItem" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>            <!-- 编辑道具模态框 -->
            <div class="modal fade" id="editModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑道具</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>道具信息</label>
                                <input type="text" class="form-control" v-bind:value="editForm.itemName + ' (编号: ' + editForm.itemNo + ')'" readonly>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>数量</label>
                                        <input type="number" class="form-control" v-model="editForm.itemCount" placeholder="请输入数量" min="0">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>位置</label>
                                        <input type="number" class="form-control" v-model="editForm.itemPos" placeholder="道具位置（可选）">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="updateItem" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 道具详情模态框 -->
            <div class="modal fade" id="detailModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">道具详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row" v-if="detailItem">
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>记录ID：</strong></td>
                                            <td>{{ detailItem.id }}</td>
                                        </tr>                                        <tr>
                                            <td><strong>用户：</strong></td>
                                            <td>{{ detailItem.username }} (ID: {{ detailItem.userId }})</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具编号：</strong></td>
                                            <td>{{ detailItem.itemNo }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具名称：</strong></td>
                                            <td>{{ detailItem.itemName }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具品质：</strong></td>
                                            <td><span class="badge" v-bind:class="detailItem.qualityCssClass">{{ detailItem.qualityText }}</span></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-sm">
                                        <tr>
                                            <td><strong>道具类型：</strong></td>
                                            <td>{{ getTypeText(detailItem.itemType) }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具数量：</strong></td>
                                            <td><strong class="text-primary">{{ formatNumber(detailItem.itemCount) }}</strong></td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具位置：</strong></td>
                                            <td>{{ detailItem.itemPos || '-' }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>道具序号：</strong></td>
                                            <td>{{ detailItem.itemSeq }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>单价：</strong></td>
                                            <td>{{ detailItem.itemPrice || 0 }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-12" v-if="detailItem.itemDescription">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> 道具描述</h6>
                                        <p>{{ detailItem.itemDescription }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

</div>

<!-- 引入Vue.js和axios -->
<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>

<script>
    const { createApp } = Vue;

    createApp({
        data() {
            return {
                // 查询条件
                queryForm: {
                    userId: '',
                    itemName: '',
                    itemType: '',
                    itemQuality: '',
                    page: 1,
                    pageSize: 15
                },
                // 道具列表
                itemList: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: 1,
                pageSize: 15,
                submitting: false,
                // 统计信息
                showStatistics: true,
                statistics: {},
                // 添加表单
                addForm: {
                    userId: '',
                    itemNo: '',
                    itemCount: 1,
                    itemPos: ''
                },
                // 编辑表单
                editForm: {
                    id: '',
                    itemName: '',
                    itemNo: '',
                    itemCount: 0,
                    itemPos: ''
                },
                // 详情信息
                detailItem: null,
                // 道具配置
                itemConfigs: [],
                selectedItemConfig: null,
                // 用户选项
                userOptions: [],
                // 道具选择下拉框相关
                showItemDropdown: false,
                itemSearchText: '',
                filteredItems: [],
                allItems: [] // 存储所有道具配置，用于过滤
            };
        },        computed: {
            // 计算可见的页码
            visiblePages() {
                const pages = [];
                const total = this.totalPages;
                const current = this.currentPage;
                
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                if (end - start < 4) {
                    if (start === 1) {
                        end = Math.min(total, start + 4);
                    } else {
                        start = Math.max(1, end - 4);
                    }
                }
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            },
            // 道具选择下拉框的显示文本
            selectedItemDisplay() {
                if (!this.addForm.itemNo) {
                    return '请选择道具';
                }
                const config = this.itemConfigs.find(c => c.itemNo == this.addForm.itemNo);
                if (!config) {
                    return this.addForm.itemNo; // 如果没有找到，显示编号
                }
                return `${config.name} (${config.itemNo})`;
            }
        },
        async mounted() {
            await this.loadUserOptions();
            await this.loadItemConfigs();
            await this.loadStatistics();
            await this.loadData();
            this.allItems = this.itemConfigs; // 初始化所有道具配置
        },
        methods: {
            // 加载用户选项
            async loadUserOptions() {
                try {
                    const response = await axios.get('/UserPet/GetUserOptions');
                    if (response.data.code === 200) {
                        this.userOptions = response.data.data || [];
                    }
                } catch (error) {
                    console.error('加载用户选项失败：', error);
                }
            },

            // 加载道具配置
            async loadItemConfigs() {
                try {
                    const response = await axios.get('/UserItem/GetItemConfigs');
                    if (response.data.success) {
                        this.itemConfigs = response.data.data || [];
                        this.allItems = this.itemConfigs; // 更新所有道具配置
                    }
                } catch (error) {
                    console.error('加载道具配置失败：', error);
                }
            },

            // 加载统计信息
            async loadStatistics() {
                try {
                    const response = await axios.get('/UserItem/GetStatistics');
                    if (response.data.success) {
                        this.statistics = response.data.data || {};
                    }
                } catch (error) {
                    console.error('加载统计信息失败：', error);
                }
            },            // 切换统计显示
            toggleStatistics() {
                this.showStatistics = !this.showStatistics;
                if (this.showStatistics) {
                    this.loadStatistics();
                }
            },

            // 加载数据
            async loadData() {
                try {
                    const response = await axios.post('/UserItem/GetList', {
                        userId: this.queryForm.userId || null,
                        itemName: this.queryForm.itemName || null,
                        itemType: this.queryForm.itemType || null,
                        itemQuality: this.queryForm.itemQuality || null,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        const result = response.data.data || {};
                        this.itemList = result.data || [];
                        this.totalCount = result.totalCount || 0;
                        this.totalPages = result.totalPages || 0;
                        this.currentPage = result.pageIndex || 1;
                    } else {
                        alert(response.data.message || '加载数据失败');
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                }
            },

            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    userId: '',
                    itemName: '',
                    itemType: '',
                    itemQuality: '',
                    page: 1,
                    pageSize: 15
                };
                this.currentPage = 1;
                this.loadData();
            },

            // 换页
            changePage(page) {
                if (page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            },            // 显示添加模态框
            showAddModal() {
                this.addForm = {
                    userId: '',
                    itemNo: '',
                    itemCount: 1,
                    itemPos: ''
                };
                this.selectedItemConfig = null;
                this.showItemDropdown = false; // 关闭下拉框
                this.itemSearchText = ''; // 清空搜索文本
                this.filteredItems = []; // 清空过滤列表
                
                const modal = new bootstrap.Modal(document.getElementById('addModal'));
                modal.show();
                
                // 添加点击外部关闭下拉框的事件监听
                setTimeout(() => {
                    document.addEventListener('click', this.handleOutsideClick);
                }, 100);
                
                // 监听模态框关闭事件
                document.getElementById('addModal').addEventListener('hidden.bs.modal', () => {
                    document.removeEventListener('click', this.handleOutsideClick);
                }, { once: true });
            },

            // 处理点击外部关闭下拉框
            handleOutsideClick(event) {
                const container = event.target.closest('.custom-select-container');
                if (!container) {
                    this.showItemDropdown = false;
                }
            },

            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    itemName: item.itemName,
                    itemNo: item.itemNo,
                    itemCount: item.itemCount,
                    itemPos: item.itemPos || ''
                };
                new bootstrap.Modal(document.getElementById('editModal')).show();
            },

            // 显示详情模态框
            showDetailModal(item) {
                this.detailItem = item;
                new bootstrap.Modal(document.getElementById('detailModal')).show();
            },

            // 添加道具
            async addItem() {
                if (!this.validateAddForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/UserItem/Add', this.addForm);
                    
                    if (response.data.success) {
                        alert('道具添加成功');
                        bootstrap.Modal.getInstance(document.getElementById('addModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '添加失败');
                    }
                } catch (error) {
                    console.error('添加道具失败：', error);
                    alert('添加道具失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },            // 更新道具
            async updateItem() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/UserItem/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert('道具更新成功');
                        bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '更新失败');
                    }
                } catch (error) {
                    console.error('更新道具失败：', error);
                    alert('更新道具失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },

            // 删除道具
            async deleteItem(item) {
                if (!confirm(`确定要删除用户 "${item.username}" 的道具 "${item.itemName}" 吗？\n\n数量：${this.formatNumber(item.itemCount)}`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserItem/Delete', { id: item.id });
                    
                    if (response.data.success) {
                        alert('道具删除成功');
                        this.loadData();
                        this.loadStatistics();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除道具失败：', error);
                    alert('删除道具失败，请重试');
                }
            },

            // 验证添加表单
            validateAddForm() {
                if (!this.addForm.userId) {
                    alert('请选择用户');
                    return false;
                }
                if (!this.addForm.itemNo) {
                    alert('请选择道具');
                    return false;
                }
                if (!this.addForm.itemCount || this.addForm.itemCount < 1) {
                    alert('请输入正确的道具数量');
                    return false;
                }
                return true;
            },            // 验证编辑表单
            validateEditForm() {
                if (this.editForm.itemCount < 0) {
                    alert('道具数量不能为负数');
                    return false;
                }
                return true;
            },

            // 格式化数字
            formatNumber(num) {
                if (num == null) return '0';
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            },

            // 获取类型文本
            getTypeText(type) {
                const typeMap = {
                    'weapon': '武器',
                    'armor': '装备', 
                    'consumable': '消耗品',
                    'material': '材料',
                    'quest': '任务物品',
                    'other': '其它'
                };
                return typeMap[type] || type || '未知';
            },

            // 获取品质文本
            getQualityText(quality) {
                const qualityMap = {
                    'white': '普通',
                    'green': '精良',
                    'blue': '稀有',
                    'purple': '史诗',
                    'orange': '传说'
                };
                return qualityMap[quality] || quality || '未知';
            },

            // 获取品质CSS类
            getQualityCssClass(quality) {
                const classMap = {
                    'white': 'badge-secondary',
                    'green': 'badge-success',
                    'blue': 'badge-info',
                    'purple': 'badge-primary',
                    'orange': 'badge-warning'
                };
                return classMap[quality] || 'badge-secondary';
            },

            // 切换道具下拉框的显示状态
            toggleItemDropdown() {
                this.showItemDropdown = !this.showItemDropdown;
                if (this.showItemDropdown) {
                    this.filteredItems = this.allItems; // 显示所有道具
                }
            },

            // 过滤道具列表
            filterItems() {
                const searchText = this.itemSearchText.toLowerCase();
                this.filteredItems = this.allItems.filter(item => 
                    item.name.toLowerCase().includes(searchText) || 
                    item.itemNo.toString().includes(searchText)
                );
            },

            // 选择道具
            selectItem(item) {
                this.addForm.itemNo = item.itemNo;
                this.selectedItemConfig = item;
                this.showItemDropdown = false;
                this.itemSearchText = '';
                this.filteredItems = [];
            }
        }
    }).mount('#userItemApp');
</script>