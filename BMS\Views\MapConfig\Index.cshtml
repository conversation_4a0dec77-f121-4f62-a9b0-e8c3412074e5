@{
    ViewData["Title"] = "地图配置管理";
}

<div id="mapConfigApp">
    <!-- 页面标题和面包屑导航 -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>地图配置管理</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">首页</a></li>
                        <li class="breadcrumb-item active">地图配置管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容 -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">地图配置列表</h5>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary" v-on:click="showCreateModal">
                                    <i class="fas fa-plus"></i> 新增地图配置
                                </button>
                            </div>
                        </div>
                <div class="card-body">
                    <!-- 搜索区域 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label for="search-map-id" class="form-label">地图ID</label>
                            <input type="number" class="form-control" id="search-map-id" v-model="searchForm.mapId" placeholder="请输入地图ID">
                        </div>
                        <div class="col-md-3">
                            <label for="search-map-name" class="form-label">地图名称</label>
                            <input type="text" class="form-control" id="search-map-name" v-model="searchForm.mapName" placeholder="请输入地图名称">
                        </div>
                        <div class="col-md-3">
                            <label for="search-atlas-name" class="form-label">图集名称</label>
                            <input type="text" class="form-control" id="search-atlas-name" v-model="searchForm.atlastName" placeholder="请输入图集名称">
                        </div>
                        <div class="col-md-3">
                            <label for="search-map-type" class="form-label">地图类型</label>
                            <input type="number" class="form-control" id="search-map-type" v-model="searchForm.mapType" placeholder="请输入地图类型">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <button type="button" class="btn btn-info" v-on:click="searchMapConfigs">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                            <button type="button" class="btn btn-secondary ms-2" v-on:click="resetSearch">
                                <i class="fas fa-undo"></i> 重置
                            </button>
                        </div>
                    </div>



                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>地图ID</th>
                                    <th>地图名称</th>
                                    <th>图集名称</th>
                                    <th>地图描述</th>
                                    <th>背景图片</th>
                                    <th>地图大小</th>
                                    <th>地图类型</th>
                                    <th>背景音乐</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="dataLoading">
                                    <td colspan="10" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">数据加载中...</span>
                                        </div>
                                        <div class="mt-2">数据加载中...</div>
                                    </td>
                                </tr>
                                <tr v-else-if="mapConfigs.length === 0">
                                    <td colspan="10" class="text-center py-4 text-muted">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <div>暂无数据</div>
                                    </td>
                                </tr>
                                <tr v-else v-for="item in mapConfigs" v-bind:key="item.id">
                                    <td>{{ item.id }}</td>
                                    <td>{{ item.mapId }}</td>
                                    <td>{{ item.mapName }}</td>
                                    <td>{{ item.atlastName || '-' }}</td>
                                    <td>{{ item.mapDesc || '-' }}</td>
                                    <td>{{ item.background || '-' }}</td>
                                    <td>{{ item.mapSize }}</td>
                                    <td>{{ item.mapType }}</td>
                                    <td>{{ item.bgm || '-' }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" v-on:click="showEditModal(item)" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-danger" v-on:click="deleteMapConfig(item.id)" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <span class="text-muted">
                                显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} 到 
                                {{ Math.min(pagination.page * pagination.pageSize, pagination.totalCount) }} 条，
                                共 {{ pagination.totalCount }} 条记录
                            </span>
                        </div>
                        <div class="col-md-6">
                            <nav>
                                <ul class="pagination justify-content-end">
                                    <li class="page-item" v-bind:class="{ disabled: pagination.page <= 1 }">
                                        <button class="page-link" v-on:click="changePage(pagination.page - 1)">上一页</button>
                                    </li>
                                    <li class="page-item" v-for="page in getPageNumbers()" v-bind:key="page" v-bind:class="{ active: page === pagination.page }">
                                        <button class="page-link" v-on:click="changePage(page)">{{ page }}</button>
                                    </li>
                                    <li class="page-item" v-bind:class="{ disabled: pagination.page >= pagination.totalPages }">
                                        <button class="page-link" v-on:click="changePage(pagination.page + 1)">下一页</button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新增/编辑模态框 -->
<div class="modal fade" id="mapConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ modalTitle }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">地图ID <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" v-model="formData.mapId" v-bind:class="{ 'is-invalid': formErrors.mapId }">
                                <div class="invalid-feedback" v-if="formErrors.mapId">{{ formErrors.mapId }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">地图名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" v-model="formData.mapName" v-bind:class="{ 'is-invalid': formErrors.mapName }">
                                <div class="invalid-feedback" v-if="formErrors.mapName">{{ formErrors.mapName }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">图集名称</label>
                                <input type="text" class="form-control" v-model="formData.atlastName" placeholder="请输入图集名称">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">地图描述</label>
                                <textarea class="form-control" rows="3" v-model="formData.mapDesc" placeholder="请输入地图描述"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">地图大小</label>
                                <input type="number" class="form-control" v-model="formData.mapSize">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">地图类型</label>
                                <input type="number" class="form-control" v-model="formData.mapType">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">类型（备用）</label>
                                <input type="number" class="form-control" v-model="formData.type">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">背景图片</label>
                                <input type="text" class="form-control" v-model="formData.background" placeholder="背景图片路径">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">地图图标</label>
                                <input type="text" class="form-control" v-model="formData.ico" placeholder="地图图标路径">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">背景音乐</label>
                                <input type="text" class="form-control" v-model="formData.bgm" placeholder="背景音乐路径">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">音量 (0.00-1.00)</label>
                                <input type="number" class="form-control" v-model="formData.bgmVolume" step="0.01" min="0" max="1">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" v-model="formData.bgmLoop">
                                <label class="form-check-label">循环播放</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" v-model="formData.bgmPlay">
                                <label class="form-check-label">播放音乐</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" v-model="formData.bgmMute">
                                <label class="form-check-label">静音</label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" v-model="formData.bgmPause">
                                <label class="form-check-label">暂停</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" v-on:click="saveMapConfig" v-bind:disabled="loading">
                    <span v-if="loading" class="spinner-border spinner-border-sm me-1"></span>
                    {{ isEdit ? '更新' : '保存' }}
                </button>
            </div>
        </div>
    </div>
</div>
</div>

<script>
// 确保Vue已加载
if (typeof Vue === 'undefined') {
    console.error('Vue.js is not loaded!');
}

const mapConfigVueApp = Vue.createApp({
    data() {
        return {
            mapConfigs: [],
            dataLoading: true,
            searchForm: {
                mapId: null,
                mapName: '',
                atlastName: '',
                mapType: null
            },
            pagination: {
                page: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0
            },
            formData: {
                id: 0,
                mapId: null,
                mapName: '',
                atlastName: '',
                mapDesc: '',
                mapSize: 0,
                mapType: 0,
                background: '',
                bgm: '',
                bgmLoop: false,
                bgmVolume: 1.00,
                bgmPlay: true,
                bgmMute: false,
                bgmPause: false,
                ico: '',
                type: 0
            },
            formErrors: {},
            loading: false,
            isEdit: false,
            modalTitle: ''
        };
    },
    mounted() {
        console.log('地图配置管理页面已加载');
        this.loadMapConfigs();
    },
    methods: {
        // 加载地图配置列表
        async loadMapConfigs() {
            this.dataLoading = true;
            try {
                const params = {
                    page: this.pagination.page,
                    pageSize: this.pagination.pageSize,
                    ...this.searchForm
                };
                
                const params_str = new URLSearchParams(params).toString();
                const response = await fetch('/MapConfig/GetPagedList?' + params_str);
                const data = await response.json();
                console.log('加载地图配置列表 - 响应:', data);
                
                if (data.success) {
                    if (Array.isArray(data.data)) {
                        this.mapConfigs = [...data.data];
                        console.log(`成功加载 ${data.data.length} 条地图配置记录`);
                    } else {
                        console.error('API返回的data不是数组:', data.data);
                        this.mapConfigs = [];
                    }
                    
                    this.pagination.totalCount = data.totalCount || 0;
                    this.pagination.totalPages = data.totalPages || 0;
                } else {
                    console.error('API返回失败:', data.message);
                    toastr.error('加载数据失败：' + data.message);
                }
            } catch (error) {
                console.error('加载地图配置列表失败：', error);
                toastr.error('加载数据失败');
            } finally {
                this.dataLoading = false;
            }
        },
        
        // 搜索
        searchMapConfigs() {
            this.pagination.page = 1;
            this.loadMapConfigs();
        },
        
        // 重置搜索条件
        resetSearch() {
            this.searchForm = {
                mapId: null,
                mapName: '',
                atlastName: '',
                mapType: null
            };
            this.pagination.page = 1;
            this.loadMapConfigs();
        },
        
        // 分页
        changePage(page) {
            if (page >= 1 && page <= this.pagination.totalPages) {
                this.pagination.page = page;
                this.loadMapConfigs();
            }
        },
        
        // 获取页码数组
        getPageNumbers() {
            const pages = [];
            const start = Math.max(1, this.pagination.page - 2);
            const end = Math.min(this.pagination.totalPages, start + 4);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        // 显示新增模态框
        showCreateModal() {
            console.log('showCreateModal clicked');
            this.isEdit = false;
            this.modalTitle = '新增地图配置';
            this.resetForm();
            // 使用Vue nextTick确保数据更新后再显示模态框
            this.$nextTick(() => {
                const modal = new bootstrap.Modal(document.getElementById('mapConfigModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();
            });
        },
        
        // 显示编辑模态框
        showEditModal(item) {
            console.log('showEditModal clicked', item);
            this.isEdit = true;
            this.modalTitle = '编辑地图配置';
            // 直接使用camelCase字段名
            this.formData = {
                id: item.id,
                mapId: item.mapId,
                mapName: item.mapName,
                atlastName: item.atlastName,
                mapDesc: item.mapDesc,
                mapSize: item.mapSize,
                mapType: item.mapType,
                background: item.background,
                bgm: item.bgm,
                bgmLoop: item.bgmLoop,
                bgmVolume: item.bgmVolume,
                bgmPlay: item.bgmPlay,
                bgmMute: item.bgmMute,
                bgmPause: item.bgmPause,
                ico: item.ico,
                type: item.type
            };
            this.formErrors = {};
            // 使用Vue nextTick确保数据更新后再显示模态框
            this.$nextTick(() => {
                const modal = new bootstrap.Modal(document.getElementById('mapConfigModal'), {
                    backdrop: 'static',
                    keyboard: false
                });
                modal.show();
            });
        },
        
        // 重置表单
        resetForm() {
            this.formData = {
                id: 0,
                mapId: null,
                mapName: '',
                atlastName: '',
                mapDesc: '',
                mapSize: 0,
                mapType: 0,
                background: '',
                bgm: '',
                bgmLoop: false,
                bgmVolume: 1.00,
                bgmPlay: true,
                bgmMute: false,
                bgmPause: false,
                ico: '',
                type: 0
            };
            this.formErrors = {};
        },
        
        // 验证表单
        validateForm() {
            this.formErrors = {};
            
            if (!this.formData.mapId) {
                this.formErrors.mapId = '地图ID不能为空';
            }
            
            if (!this.formData.mapName) {
                this.formErrors.mapName = '地图名称不能为空';
            }
            
            return Object.keys(this.formErrors).length === 0;
        },
        
        // 保存地图配置
        async saveMapConfig() {
            if (!this.validateForm()) {
                return;
            }
            
            this.loading = true;
            
            try {
                const url = this.isEdit ? '/MapConfig/Update' : '/MapConfig/Create';
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.formData)
                });
                const data = await response.json();
                
                if (data.success) {
                    toastr.success(data.message);
                    // 关闭模态框
                    const modalElement = document.getElementById('mapConfigModal');
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                    this.loadMapConfigs();
                } else {
                    toastr.error(data.message);
                }
            } catch (error) {
                console.error('保存地图配置失败：', error);
                toastr.error('保存失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 删除地图配置
        async deleteMapConfig(id) {
            console.log('deleteMapConfig clicked', id);
            if (!confirm('确定要删除这个地图配置吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/MapConfig/Delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ id })
                });
                const data = await response.json();
                
                if (data.success) {
                    toastr.success(data.message);
                    this.loadMapConfigs();
                } else {
                    toastr.error(data.message);
                }
            } catch (error) {
                console.error('删除地图配置失败：', error);
                toastr.error('删除失败');
            }
        }
    }
});

// 确保DOM完全加载后挂载Vue应用
function mountVueApp() {
    const element = document.getElementById('mapConfigApp');
    const modal = document.getElementById('mapConfigModal');
    if (element && modal) {
        console.log('Mounting Vue app to #mapConfigApp');
        console.log('Modal element found:', modal);
        mapConfigVueApp.mount('#mapConfigApp');
    } else {
        console.error('Element not found! mapConfigApp:', !!element, 'modal:', !!modal);
        // 重试
        setTimeout(mountVueApp, 50);
    }
}

// 使用多种方式确保DOM准备就绪
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', mountVueApp);
} else {
    setTimeout(mountVueApp, 50);
}
</script> 