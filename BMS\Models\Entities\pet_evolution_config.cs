﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace BMS.Models.Entities
{
    ///<summary>
    ///宠物进化配置表
    ///</summary>
    [SugarTable("pet_evolution_config")]
    public partial class pet_evolution_config
    {
           public pet_evolution_config(){


           }
           /// <summary>
           /// Desc:自增主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:宠物编号（关联pet_config.pet_no）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int pet_no {get;set;}

           /// <summary>
           /// Desc:进化路线类型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string evolution_type {get;set;}

           /// <summary>
           /// Desc:进化后宠物编号
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int target_pet_no {get;set;}

           /// <summary>
           /// Desc:所需等级
           /// Default:40
           /// Nullable:False
           /// </summary>           
           public int required_level {get;set;}

           /// <summary>
           /// Desc:所需道具ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? required_item_id {get;set;}

           /// <summary>
           /// Desc:所需道具数量
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public int? required_item_count {get;set;}

           /// <summary>
           /// Desc:消耗金币
           /// Default:1000
           /// Nullable:True
           /// </summary>           
           public long? cost_gold {get;set;}

           /// <summary>
           /// Desc:最小成长加成
           /// Default:0.100
           /// Nullable:True
           /// </summary>           
           public decimal? growth_min {get;set;}

           /// <summary>
           /// Desc:最大成长加成
           /// Default:0.500
           /// Nullable:True
           /// </summary>           
           public decimal? growth_max {get;set;}

           /// <summary>
           /// Desc:成功率(%)
           /// Default:100.00
           /// Nullable:True
           /// </summary>           
           public decimal? success_rate {get;set;}

           /// <summary>
           /// Desc:创建时间
           /// Default:CURRENT_TIMESTAMP
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
