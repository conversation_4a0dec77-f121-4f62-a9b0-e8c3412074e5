@{
    ViewData["Title"] = "掉落配置管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">掉落配置管理</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">主页</a></li>
                        <li class="breadcrumb-item active">掉落配置管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card" id="dropConfigApp">
                        <div class="card-header">
                            <h3 class="card-title">掉落配置列表</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary" v-on:click="openCreateModal">
                                    <i class="fas fa-plus"></i> 新增掉落配置
                                </button>
                            </div>
                        </div>
                        
                        <!-- 搜索区域 -->
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-2">
                                    <select class="form-control" v-model="searchForm.mapId" v-on:change="onMapChange">
                                        <option value="">请选择地图</option>
                                        <option v-for="option in mapOptions" v-bind:key="option.value" v-bind:value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" placeholder="地图名称" v-model="searchForm.mapName">
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" v-model="searchForm.dropType">
                                        <option value="">请选择掉落类型</option>
                                        <option v-for="option in dropTypeOptions" v-bind:key="option.value" v-bind:value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <select class="form-control" v-model="searchForm.monsterId" v-bind:disabled="!searchForm.mapId">
                                        <option value="">请选择怪物</option>
                                        <option v-for="option in monsterOptions" v-bind:key="option.value" v-bind:value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" placeholder="道具ID" v-model="searchForm.itemId">
                                </div>
                                <div class="col-md-2">
                                    <input type="text" class="form-control" placeholder="道具名称" v-model="searchForm.itemName">
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <button type="button" class="btn btn-info" v-on:click="search">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="button" class="btn btn-secondary ml-2" v-on:click="reset">
                                        <i class="fas fa-sync"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-danger ml-2" v-on:click="batchDelete" v-bind:disabled="selectedIds.length === 0">
                                        <i class="fas fa-trash"></i> 批量删除
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 数据表格 -->
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px;">
                                                <input type="checkbox" v-model="selectAll" v-on:change="toggleSelectAll">
                                            </th>
                                            <th>ID</th>
                                            <th>地图名称</th>
                                            <th>掉落类型</th>
                                            <th>怪物名称</th>
                                            <th>道具数量</th>
                                            <th>备注</th>
                                            <th>创建时间</th>
                                            <th style="width: 150px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="item in dropConfigs" v-bind:key="item.id">
                                            <td>
                                                <input type="checkbox" v-model="selectedIds" v-bind:value="item.id">
                                            </td>
                                            <td>{{ item.id }}</td>
                                            <td>{{ item.mapName }}</td>
                                            <td>{{ item.dropType }}</td>
                                            <td>{{ item.monsterName || '-' }}</td>
                                            <td>
                                                <span v-if="item.dropItemsJson">
                                                    <span class="badge-info">{{ parseDropItems(item.dropItemsJson).length }}种道具</span>
                                                </span>
                                                <span v-else-if="item.itemId">
                                                    <span class="badge-secondary">1种道具</span>
                                                </span>
                                                <span v-else>-</span>
                                            </td>
                                            <td>{{ item.remark || '-' }}</td>
                                            <td>{{ formatDateTime(item.createTime) }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-warning" v-on:click="openUpdateModal(item)">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger ml-1" v-on:click="deleteItem(item.id)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr v-if="dropConfigs.length === 0">
                                            <td colspan="9" class="text-center">暂无数据</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <div class="row" v-if="totalCount > 0">
                                <div class="col-sm-5">
                                    <div class="dataTables_info">
                                        显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条
                                    </div>
                                </div>
                                <div class="col-sm-7">
                                    <div class="dataTables_paginate paging_simple_numbers float-right">
                                        <ul class="pagination">
                                            <li class="paginate_button page-item" v-bind:class="{ disabled: currentPage === 1 }">
                                                <a href="javascript:void(0)" class="page-link" v-on:click="changePage(currentPage - 1)">上一页</a>
                                            </li>
                                            <li class="paginate_button page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{ active: page === currentPage }">
                                                <a href="javascript:void(0)" class="page-link" v-on:click="changePage(page)">{{ page }}</a>
                                            </li>
                                            <li class="paginate_button page-item" v-bind:class="{ disabled: currentPage === totalPages }">
                                                <a href="javascript:void(0)" class="page-link" v-on:click="changePage(currentPage + 1)">下一页</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 新增/编辑模态框 -->
                        <div class="modal fade" id="dropConfigModal" tabindex="-1" role="dialog" style="z-index: 9999;">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title">{{ isEdit ? '编辑掉落配置' : '新增掉落配置' }}</h4>
                                        <button type="button" class="close" v-on:click="closeModal">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <form>
                                            <div class="form-group">
                                                <label>地图 <span class="text-danger">*</span></label>
                                                <select class="form-control" v-model="formData.mapId" v-on:change="onFormMapChange">
                                                    <option value="">请选择地图</option>
                                                    <option v-for="option in mapOptions" v-bind:key="option.value" v-bind:value="option.value">
                                                        {{ option.label }}
                                                    </option>
                                                </select>
                                                <div class="text-danger" v-if="formErrors.mapId">{{ formErrors.mapId }}</div>
                                            </div>
                                            <div class="form-group">
                                                <label>掉落类型 <span class="text-danger">*</span></label>
                                                <select class="form-control" v-model="formData.dropType" v-on:change="onDropTypeChange">
                                                    <option value="">请选择掉落类型</option>
                                                    <option v-for="option in dropTypeOptions" v-bind:key="option.value" v-bind:value="option.value">
                                                        {{ option.label }}
                                                    </option>
                                                </select>
                                                <div class="text-danger" v-if="formErrors.dropType">{{ formErrors.dropType }}</div>
                                            </div>
                                            <div class="form-group" v-if="formData.dropType === '怪物掉落'">
                                                <label>怪物 <span class="text-danger">*</span></label>
                                                <select class="form-control" v-model="formData.monsterId" v-bind:disabled="!formData.mapId">
                                                    <option value="">请选择怪物</option>
                                                    <option v-for="option in formMonsterOptions" v-bind:key="option.value" v-bind:value="option.value">
                                                        {{ option.label }}
                                                    </option>
                                                </select>
                                                <div class="text-danger" v-if="formErrors.monsterId">{{ formErrors.monsterId }}</div>
                                            </div>
                                            <!-- 道具配置区域 -->
                                            <div class="form-group">
                                                <label>掉落道具配置 <span class="text-danger">*</span></label>
                                                
                                                <!-- 添加道具区域 -->
                                                <div class="card card-outline card-info mb-3">
                                                    <div class="card-header">
                                                        <h3 class="card-title">添加掉落道具</h3>
                                                </div>
                                                    <div class="card-body">
                                                        <div class="row mb-2">
                                                            <div class="col-md-6">
                                                                <label>选择道具</label>
                                                                <input type="text" class="form-control form-control-sm mb-1" placeholder="搜索道具..." v-model="itemSearchText" v-on:input="filterItems">
                                                                <select class="form-control" v-model="currentItem.itemId" size="3" style="height: auto; min-height: 60px; max-height: 120px;">
                                                    <option value="">请选择道具</option>
                                                    <option v-for="option in filteredItemOptions" v-bind:key="option.value" v-bind:value="option.value">
                                                        [{{ option.value }}] {{ option.label }}
                                                    </option>
                                                </select>
                                            </div>
                                                            <div class="col-md-6">
                                            <div class="row">
                                                                    <div class="col-md-12 mb-2">
                                                                        <label>掉落概率</label>
                                                                        <input type="number" class="form-control" v-model.number="currentItem.dropRate" step="0.01" min="0" max="1" placeholder="0.00-1.00">
                                                                    </div>
                                                <div class="col-md-6">
                                                                        <label>最小数量</label>
                                                                        <input type="number" class="form-control" v-model.number="currentItem.minCount" min="1" placeholder="最小数量">
                                                </div>
                                                <div class="col-md-6">
                                                                        <label>最大数量</label>
                                                                        <input type="number" class="form-control" v-model.number="currentItem.maxCount" min="1" placeholder="最大数量">
                                                    </div>
                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-md-12">
                                                                <button type="button" class="btn btn-sm btn-success" v-on:click="addDropItem" v-bind:disabled="!currentItem.itemId">
                                                                    <i class="fas fa-plus"></i> 添加道具
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 已配置的道具列表 -->
                                                <div class="card card-outline card-secondary" v-if="formData.dropItems.length > 0">
                                                    <div class="card-header">
                                                        <h3 class="card-title">已配置的掉落道具 ({{ formData.dropItems.length }})</h3>
                                                    </div>
                                                    <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                                        <div class="table-responsive">
                                                            <table class="table table-sm table-bordered">
                                                                <thead>
                                                                    <tr>
                                                                        <th>道具</th>
                                                                        <th>概率</th>
                                                                        <th>数量</th>
                                                                        <th style="width: 60px;">操作</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr v-for="(item, index) in formData.dropItems" v-bind:key="index">
                                                                        <td>
                                                                            [{{ item.itemId }}] {{ getItemName(item.itemId) }}
                                                                            <small class="text-muted d-block" v-if="!getItemName(item.itemId) || getItemName(item.itemId).includes('道具')">
                                                                                调试: itemId={{ item.itemId }}, 道具总数={{ itemOptions.length }}
                                                                            </small>
                                                                        </td>
                                                                        <td>{{ (item.dropRate * 100).toFixed(2) }}%</td>
                                                                        <td>{{ item.minCount === item.maxCount ? item.minCount : (item.minCount + '-' + item.maxCount) }}</td>
                                                                        <td>
                                                                            <button type="button" class="btn btn-xs btn-danger" v-on:click="removeDropItem(index)">
                                                                                <i class="fas fa-trash"></i>
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="text-danger" v-if="formErrors.dropItems">{{ formErrors.dropItems }}</div>
                                                <small class="form-text text-muted">
                                                    显示 {{ filteredItemOptions.length }} / {{ itemOptions.length }} 个道具可选
                                                </small>
                                            </div>
                                            <div class="form-group">
                                                <label>备注</label>
                                                <textarea class="form-control" v-model="formData.remark" rows="3" placeholder="请输入备注信息"></textarea>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" v-on:click="closeModal">取消</button>
                                        <button type="button" class="btn btn-primary" v-on:click="saveDropConfig" v-bind:disabled="loading">
                                            {{ loading ? '保存中...' : '保存' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* 确保模态框不被遮挡 */
.modal {
    z-index: 9999 !important;
}

.modal-backdrop {
    display: none !important;
}

.modal-dialog {
    z-index: 10000 !important;
    margin: 30px auto;
}

/* 确保Vue应用不影响页面导航 */
#dropConfigApp {
    position: relative;
}

#dropConfigApp * {
    pointer-events: auto;
}
</style>

<script src="~/lib/vue/vue.global.js"></script>
<script src="~/lib/axios.min.js"></script>
<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="~/plugins/toastr/toastr.min.js"></script>
<script>
try {
    console.log('Vue version:', Vue.version);
    console.log('开始初始化Vue应用...');

    if (typeof Vue === 'undefined') {
        throw new Error('Vue is not loaded');
    }

    const { createApp } = Vue;

    const app = createApp({
        data() {
            return {
                // 数据列表
                dropConfigs: [],
                totalCount: 0,
                currentPage: 1,
                pageSize: 10,
                
                // 搜索表单
                searchForm: {
                    mapId: '',
                    mapName: '',
                    dropType: '',
                    monsterId: '',
                    itemId: '',
                    itemName: ''
                },
                
                // 表单数据
                formData: {
                    id: 0,
                    mapId: '',
                    dropType: '',
                    monsterId: '',
                    remark: '',
                    dropItems: [] // 掉落道具列表
                },
                
                // 表单验证错误
                formErrors: {},
                
                // 选项数据
                mapOptions: [],
                dropTypeOptions: [],
                monsterOptions: [],
                formMonsterOptions: [],
                itemOptions: [],
                
                // 状态
                loading: false,
                isEdit: false,
                selectAll: false,
                selectedIds: [],
                
                // 道具搜索
                itemSearchText: '',
                filteredItemOptions: [],
                
                // 当前选择的道具
                currentItem: {
                    itemId: '',
                    dropRate: 0.1,
                    minCount: 1,
                    maxCount: 1
                }
            }
        },
        
        computed: {
        totalPages() {
            return Math.ceil(this.totalCount / this.pageSize);
        },
        
        visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            
            return pages;
                }
        },
        
        mounted() {
            console.log('Vue应用已挂载');
            this.loadOptions();
            this.loadData();
            
            // 检查依赖库是否正确加载
            console.log('jQuery版本:', typeof $ !== 'undefined' ? $.fn.jquery : '未加载');
            console.log('Bootstrap版本:', typeof bootstrap !== 'undefined' ? 'Bootstrap 5' : '未加载或Bootstrap 4');
            
            // 添加ESC键关闭模态框
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && $('#dropConfigModal').hasClass('show')) {
                    this.closeModal();
                }
            });
        },
        
        methods: {
        // 加载选项数据
        async loadOptions() {
            try {
                const [mapRes, dropTypeRes, itemRes] = await Promise.all([
                    axios.get('/DropConfig/GetMapOptions'),
                    axios.get('/DropConfig/GetDropTypeOptions'),
                    axios.get('/DropConfig/GetItemOptions')
                ]);
                
                this.mapOptions = mapRes.data.data || [];
                this.dropTypeOptions = dropTypeRes.data.data || [];
                this.itemOptions = itemRes.data.data || [];
                this.filteredItemOptions = [...this.itemOptions]; // 初始化过滤后的道具选项
                
                console.log('地图选项:', this.mapOptions);
                console.log('掉落类型选项:', this.dropTypeOptions);
                console.log('道具选项:', this.itemOptions);
            } catch (error) {
                console.error('加载选项失败:', error);
                this.showError('加载选项失败');
            }
        },
        
        // 加载数据
        async loadData() {
            try {
                this.loading = true;
                
                // 构建查询参数
                const queryData = {
                    mapId: this.searchForm.mapId ? parseInt(this.searchForm.mapId) : null,
                    mapName: this.searchForm.mapName || '',
                    dropType: this.searchForm.dropType || '',
                    monsterId: this.searchForm.monsterId ? parseInt(this.searchForm.monsterId) : null,
                    itemId: this.searchForm.itemId || '',
                    itemName: this.searchForm.itemName || '',
                    page: this.currentPage,
                    pageSize: this.pageSize
                };
                
                console.log('发送查询数据:', queryData);
                const response = await axios.post('/DropConfig/GetList', queryData);
                console.log('获取响应数据:', response.data);
                
                if (response.data.code === 200) {
                    this.dropConfigs = response.data.data || [];
                    this.totalCount = response.data.total || 0;
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.showError('加载数据失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 搜索
        search() {
            this.currentPage = 1;
            this.loadData();
        },
        
        // 重置搜索
        reset() {
            this.searchForm = {
                mapId: '',
                mapName: '',
                dropType: '',
                monsterId: '',
                itemId: '',
                itemName: ''
            };
            this.monsterOptions = [];
            this.search();
        },
        
        // 地图变化事件
        async onMapChange() {
            this.searchForm.monsterId = '';
            this.monsterOptions = [];
            
            if (this.searchForm.mapId) {
                await this.loadMonsterOptions(this.searchForm.mapId);
            }
        },
        
        // 表单地图变化事件
        async onFormMapChange() {
            this.formData.monsterId = '';
            this.formMonsterOptions = [];
            
            if (this.formData.mapId) {
                await this.loadFormMonsterOptions(this.formData.mapId);
            }
        },
        
        // 掉落类型变化事件
        onDropTypeChange() {
            if (this.formData.dropType !== '怪物掉落') {
                this.formData.monsterId = '';
            }
        },
        
        // 加载怪物选项
        async loadMonsterOptions(mapId) {
            try {
                const response = await axios.get(`/DropConfig/GetMonsterOptions?mapId=${mapId}`);
                if (response.data.code === 200) {
                    this.monsterOptions = response.data.data || [];
                }
            } catch (error) {
                console.error('加载怪物选项失败:', error);
            }
        },
        
        // 加载表单怪物选项
        async loadFormMonsterOptions(mapId) {
            try {
                const response = await axios.get(`/DropConfig/GetMonsterOptions?mapId=${mapId}`);
                if (response.data.code === 200) {
                    this.formMonsterOptions = response.data.data || [];
                }
            } catch (error) {
                console.error('加载怪物选项失败:', error);
            }
        },
        
        // 翻页
        changePage(page) {
            if (page < 1 || page > this.totalPages || page === this.currentPage) {
                return;
            }
            this.currentPage = page;
            this.loadData();
        },
        
        // 全选/取消全选
        toggleSelectAll() {
            if (this.selectAll) {
                this.selectedIds = this.dropConfigs.map(item => item.id);
            } else {
                this.selectedIds = [];
            }
        },
        
        // 打开新增模态框
        openCreateModal() {
            console.log('打开新增模态框');
            this.isEdit = false;
            this.formData = {
                id: 0,
                mapId: '',
                dropType: '',
                monsterId: '',
                remark: '',
                dropItems: []
            };
            this.currentItem = {
                itemId: '',
                dropRate: 0.1,
                minCount: 1,
                maxCount: 1
            };
            this.formErrors = {};
            this.formMonsterOptions = [];
            this.itemSearchText = '';
            this.filteredItemOptions = [...this.itemOptions];
            
            // 使用原生Bootstrap方式打开模态框
            const modalElement = document.getElementById('dropConfigModal');
            if (modalElement) {
                // 确保Bootstrap已加载
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(modalElement, {
                        backdrop: false,
                        keyboard: true
                    });
                    modal.show();
                } else {
                    // 回退到jQuery方式
            $('#dropConfigModal').modal({
                show: true,
                backdrop: false,
                keyboard: true
            });
                }
            } else {
                console.error('找不到模态框元素');
            }
        },
        
        // 打开编辑模态框
        async openUpdateModal(item) {
            console.log('打开编辑模态框, 原始数据:', item);
            
            // 确保道具选项已加载
            if (!this.itemOptions || this.itemOptions.length === 0) {
                console.log('重新加载道具选项...');
                await this.loadOptions();
            }
            
            this.isEdit = true;
            
            // 根据数据类型决定如何加载掉落道具
            let dropItems = [];
            if (item.dropItemsJson) {
                // 新格式：多道具JSON
                try {
                    dropItems = this.parseDropItems(item.dropItemsJson);
                    console.log('解析多道具配置成功:', dropItems);
                } catch (error) {
                    console.error('解析多道具配置失败:', error);
                    dropItems = [];
                }
            } else if (item.itemId) {
                // 旧格式：单道具
                dropItems = [{
                    itemId: item.itemId.toString(), // 确保为字符串
                    dropRate: parseFloat(item.dropRate) || 0.1,
                    minCount: parseInt(item.minCount) || 1,
                    maxCount: parseInt(item.maxCount) || 1
                }];
                console.log('转换单道具配置:', dropItems);
            }
            
            // 确保mapId和monsterId为字符串（用于select绑定）
            this.formData = {
                id: item.id,
                mapId: item.mapId ? item.mapId.toString() : '',
                dropType: item.dropType || '',
                monsterId: item.monsterId ? item.monsterId.toString() : '',
                remark: item.remark || '',
                dropItems: dropItems
            };
            
            console.log('设置表单数据:', this.formData);
            console.log('当前地图选项:', this.mapOptions);
            console.log('当前道具选项数量:', this.itemOptions.length);
            
            this.currentItem = {
                itemId: '',
                dropRate: 0.1,
                minCount: 1,
                maxCount: 1
            };
            this.formErrors = {};
            
            // 加载对应地图的怪物选项
            if (this.formData.mapId) {
                await this.loadFormMonsterOptions(this.formData.mapId);
            }
            
            this.itemSearchText = '';
            this.filteredItemOptions = [...this.itemOptions];
            
            // 等待DOM更新
            await new Promise(resolve => setTimeout(resolve, 100));
            
            // 使用原生Bootstrap方式打开模态框
            const modalElement = document.getElementById('dropConfigModal');
            if (modalElement) {
                // 确保Bootstrap已加载
                if (typeof bootstrap !== 'undefined') {
                    const modal = new bootstrap.Modal(modalElement, {
                        backdrop: false,
                        keyboard: true
                    });
                    modal.show();
                } else {
                    // 回退到jQuery方式
            $('#dropConfigModal').modal({
                show: true,
                backdrop: false,
                keyboard: true
            });
                }
            } else {
                console.error('找不到模态框元素');
            }
        },
        
        // 关闭模态框
        closeModal() {
            const modalElement = document.getElementById('dropConfigModal');
            if (modalElement) {
                // 使用原生Bootstrap方式关闭模态框
                if (typeof bootstrap !== 'undefined') {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                } else {
                    // 回退到jQuery方式
            $('#dropConfigModal').modal('hide');
                }
            }
            
            // 清除可能残留的backdrop
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
        },
        
        // 保存掉落配置
        async saveDropConfig() {
            if (!this.validateForm()) {
                return;
            }
            
            try {
                this.loading = true;
                const url = this.isEdit ? '/DropConfig/UpdateMulti' : '/DropConfig/CreateMulti';
                
                // 构建保存数据，确保数据类型正确
                const saveData = {
                    id: this.formData.id || 0,
                    mapId: this.formData.mapId ? parseInt(this.formData.mapId) : 0,
                    dropType: this.formData.dropType,
                    monsterId: this.formData.monsterId && this.formData.monsterId !== '' ? parseInt(this.formData.monsterId) : null,
                    dropItems: this.formData.dropItems.map(item => ({
                        itemId: item.itemId,
                        dropRate: parseFloat(item.dropRate),
                        minCount: parseInt(item.minCount),
                        maxCount: parseInt(item.maxCount)
                    })),
                    remark: this.formData.remark || ''
                };
                
                console.log('发送保存数据:', saveData);
                const response = await axios.post(url, saveData);
                console.log('保存响应:', response.data);
                
                if (response.data.code === 200) {
                    this.showSuccess(response.data.message);
                    this.closeModal();
                    this.loadData();
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('保存失败:', error);
                this.showError('保存失败');
            } finally {
                this.loading = false;
            }
        },
        
        // 表单验证
        validateForm() {
            this.formErrors = {};
            
            if (!this.formData.mapId) {
                this.formErrors.mapId = '请选择地图';
            }
            
            if (!this.formData.dropType) {
                this.formErrors.dropType = '请选择掉落类型';
            }
            
            if (this.formData.dropType === '怪物掉落' && !this.formData.monsterId) {
                this.formErrors.monsterId = '请选择怪物';
            }
            
            if (!this.formData.dropItems || this.formData.dropItems.length === 0) {
                this.formErrors.dropItems = '请至少添加一个掉落道具';
            }
            
            return Object.keys(this.formErrors).length === 0;
        },
        
        // 删除单个项目
        async deleteItem(id) {
            if (!confirm('确定要删除这个掉落配置吗？')) {
                return;
            }
            
            try {
                const response = await axios.post('/DropConfig/Delete', { id: id });
                if (response.data.code === 200) {
                    this.showSuccess(response.data.message);
                    this.loadData();
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('删除失败:', error);
                this.showError('删除失败');
            }
        },
        
        // 批量删除
        async batchDelete() {
            if (this.selectedIds.length === 0) {
                this.showWarning('请选择要删除的项目');
                return;
            }
            
            if (!confirm(`确定要删除选中的 ${this.selectedIds.length} 个掉落配置吗？`)) {
                return;
            }
            
            try {
                const response = await axios.post('/DropConfig/BatchDelete', this.selectedIds);
                if (response.data.code === 200) {
                    this.showSuccess(response.data.message);
                    this.selectedIds = [];
                    this.selectAll = false;
                    this.loadData();
                } else {
                    this.showError(response.data.message);
                }
            } catch (error) {
                console.error('批量删除失败:', error);
                this.showError('批量删除失败');
            }
        },
        
        // 格式化日期时间
        formatDateTime(dateTime) {
            if (!dateTime) return '-';
            const date = new Date(dateTime);
            return date.toLocaleString('zh-CN');
        },
        
        // 显示成功消息
        showSuccess(message) {
            toastr.success(message);
        },
        
        // 显示错误消息
        showError(message) {
            toastr.error(message);
        },
        
        // 显示警告消息
        showWarning(message) {
            toastr.warning(message);
        },
        
        // 过滤道具选项
        filterItems() {
            if (!this.itemSearchText.trim()) {
                this.filteredItemOptions = [...this.itemOptions];
            } else {
                const searchText = this.itemSearchText.toLowerCase().trim();
                this.filteredItemOptions = this.itemOptions.filter(item => 
                    item.label.toLowerCase().includes(searchText) || 
                    item.value.toString().includes(searchText)
                );
            }
        },
        
        // 添加掉落道具
        addDropItem() {
            if (!this.currentItem.itemId) {
                this.showWarning('请选择道具');
                return;
            }
            
            // 检查道具是否已经添加
            if (this.formData.dropItems.some(item => item.itemId === this.currentItem.itemId)) {
                this.showWarning('该道具已经添加，请选择其他道具');
                return;
            }
            
            // 验证当前道具的数据
            if (!this.currentItem.dropRate || this.currentItem.dropRate <= 0 || this.currentItem.dropRate > 1) {
                this.showWarning('掉落概率必须在0-1之间');
                return;
            }
            
            if (!this.currentItem.minCount || this.currentItem.minCount < 1) {
                this.showWarning('最小掉落数量必须大于0');
                return;
            }
            
            if (!this.currentItem.maxCount || this.currentItem.maxCount < 1) {
                this.showWarning('最大掉落数量必须大于0');
                return;
            }
            
            if (this.currentItem.minCount > this.currentItem.maxCount) {
                this.showWarning('最大数量不能小于最小数量');
                return;
            }
            
            // 添加到掉落道具列表
            this.formData.dropItems.push({
                itemId: this.currentItem.itemId,
                dropRate: this.currentItem.dropRate,
                minCount: this.currentItem.minCount,
                maxCount: this.currentItem.maxCount
            });
            
            // 重置当前道具选择
            this.currentItem = {
                itemId: '',
                dropRate: 0.1,
                minCount: 1,
                maxCount: 1
            };
            
            this.showSuccess('道具添加成功');
        },
        
        // 移除掉落道具
        removeDropItem(index) {
            if (index >= 0 && index < this.formData.dropItems.length) {
                this.formData.dropItems.splice(index, 1);
                this.showSuccess('道具移除成功');
            }
        },
        
        // 获取道具名称
        getItemName(itemId) {
            if (!itemId) {
                console.warn('getItemName: itemId为空');
                return '未知道具';
            }
            
            if (!this.itemOptions || this.itemOptions.length === 0) {
                console.warn('getItemName: itemOptions未加载', itemId);
                return `道具${itemId}`;
            }
            
            const item = this.itemOptions.find(option => option.value === itemId || option.value === itemId.toString());
            if (!item) {
                console.warn('getItemName: 找不到道具', itemId, this.itemOptions.length);
                return `道具${itemId}`;
            }
            
            return item.label || `道具${itemId}`;
        },
        
        // 解析掉落道具JSON
        parseDropItems(dropItemsJson) {
            if (!dropItemsJson) return [];
            try {
                const items = JSON.parse(dropItemsJson);
                // 确保数据格式正确
                return items.map(item => ({
                    itemId: item.ItemId || item.itemId || '',
                    dropRate: parseFloat(item.DropRate || item.dropRate || 0.1),
                    minCount: parseInt(item.MinCount || item.minCount || 1),
                    maxCount: parseInt(item.MaxCount || item.maxCount || 1)
                }));
            } catch (error) {
                console.error('解析掉落道具失败:', error);
                return [];
            }
        }
    },
    
        watch: {
            selectedIds() {
                this.selectAll = this.selectedIds.length === this.dropConfigs.length && this.dropConfigs.length > 0;
            }
        }
});

    console.log('Vue应用创建成功，准备挂载...');
    app.mount('#dropConfigApp');
    console.log('Vue应用挂载完成');
    
    // 确保页面导航正常工作
    document.addEventListener('click', function(e) {
        // 允许面包屑导航和其他普通链接正常工作
        if (e.target.tagName === 'A' && e.target.getAttribute('href') && 
            !e.target.getAttribute('href').startsWith('javascript:') && 
            e.target.getAttribute('href') !== '#') {
            // 对于有实际href的链接，允许正常跳转
            return true;
        }
    }, true);

} catch (error) {
    console.error('Vue应用初始化失败:', error);
    document.getElementById('dropConfigApp').innerHTML = '<div class="alert alert-danger">Vue应用加载失败: ' + error.message + '</div>';
}
</script>