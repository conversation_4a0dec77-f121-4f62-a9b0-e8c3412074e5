﻿@{
    ViewData["Title"] = "首页";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);
        --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        --card-hover-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
        border-radius: 20px;
        padding: 60px 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: var(--card-shadow);
    }

    @@keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        pointer-events: none;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
        animation: slideInDown 0.8s ease-out;
    }

    @@keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero-info {
        display: flex;
        gap: 40px;
        margin-top: 30px;
        animation: slideInUp 0.8s ease-out 0.3s both;
    }

    @@keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .hero-info-item {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        padding: 15px 25px;
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .stat-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
        margin-bottom: 30px;
        animation: fadeInUp 0.6s ease-out;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--card-hover-shadow);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--card-gradient);
    }

    .stat-card.info::before {
        background: var(--info-gradient);
    }

    .stat-card.success::before {
        background: var(--success-gradient);
    }

    .stat-card.warning::before {
        background: var(--warning-gradient);
    }

    .stat-card.danger::before {
        background: var(--primary-gradient);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 20px;
        background: var(--info-gradient);
    }

    .stat-card.success .stat-icon {
        background: var(--success-gradient);
    }

    .stat-card.warning .stat-icon {
        background: var(--warning-gradient);
    }

    .stat-card.danger .stat-icon {
        background: var(--primary-gradient);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 10px;
        display: block;
    }

    .stat-label {
        color: #718096;
        font-size: 0.95rem;
        margin: 0;
        font-weight: 500;
    }

    .actions-card, .announcements-card {
        background: white;
        border-radius: 20px;
        padding: 30px;
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        border: none;
        height: 100%;
        animation: fadeInUp 0.6s ease-out 0.4s both;
    }

    .card-header-custom {
        border: none;
        background: none;
        padding: 0 0 25px 0;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .card-header-custom h5 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 600;
        color: #2d3748;
    }

    .header-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: var(--primary-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
    }

    .action-btn {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 15px 20px;
        margin-bottom: 15px;
        transition: var(--transition);
        text-align: left;
        width: 100%;
        color: #4a5568;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .action-btn:hover {
        border-color: #667eea;
        background: #f7faff;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
        color: #667eea;
    }

    .action-btn i {
        width: 20px;
        text-align: center;
    }

    .announcement-item {
        background: linear-gradient(135deg, #f8faff 0%, #f1f5ff 100%);
        border: 1px solid #e2e8f0;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        position: relative;
        transition: var(--transition);
    }

    .announcement-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .announcement-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        border-radius: 2px;
        background: var(--info-gradient);
    }

    .announcement-item.warning::before {
        background: var(--warning-gradient);
    }

    .announcement-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        background: var(--info-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 16px;
        margin-right: 15px;
        flex-shrink: 0;
    }

    .announcement-item.warning .announcement-icon {
        background: var(--warning-gradient);
    }

    .announcement-content {
        flex: 1;
    }

    .announcement-title {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 5px;
        font-size: 1rem;
    }

    .announcement-text {
        color: #718096;
        margin: 0;
        font-size: 0.9rem;
        line-height: 1.5;
    }

    @@media (max-width: 768px) {
        .hero-section {
            padding: 40px 20px;
            margin-bottom: 30px;
        }

        .hero-title {
            font-size: 2rem;
        }

        .hero-info {
            flex-direction: column;
            gap: 20px;
        }

        .stat-card {
            padding: 20px;
            margin-bottom: 20px;
        }

        .actions-card, .announcements-card {
            padding: 20px;
        }
    }

    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        height: 20px;
    }

    @@keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
</style>

<div id="homeApp">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="fas fa-home"></i> 欢迎使用口袋后台管理系统
            </h1>
            <div class="hero-info">
                <div class="hero-info-item">
                    <strong>当前用户：</strong> {{ currentUser }}
                </div>
                <div class="hero-info-item">
                    <strong>系统时间：</strong> {{ currentTime }}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 统计卡片 -->
    <div class="row">
        <div class="col-md-3 col-sm-6">
            <div class="stat-card info">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <span class="stat-number">{{ userCount }}</span>
                <p class="stat-label">注册用户总数</p>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card success">
                <div class="stat-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <span class="stat-number">{{ adminCount }}</span>
                <p class="stat-label">管理员总数</p>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card warning">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <span class="stat-number">{{ todayActiveCount }}</span>
                <p class="stat-label">今日活跃用户</p>
            </div>
        </div>
        <div class="col-md-3 col-sm-6">
            <div class="stat-card danger">
                <div class="stat-icon">
                    <i class="fas fa-server"></i>
                </div>
                <span class="stat-number">{{ systemStatus }}</span>
                <p class="stat-label">系统运行状态</p>
            </div>
        </div>
    </div>

    <!-- 功能区域 -->
    <div class="row">
        <div class="col-lg-6">
            <div class="actions-card">
                <div class="card-header-custom">
                    <div class="header-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h5>快速操作</h5>
                </div>
                <div class="d-grid gap-2">
                    <button type="button" class="action-btn" v-on:click="goToAdminManage">
                        <i class="fas fa-users-cog"></i>
                        <span>管理员管理</span>
                    </button>
                    <button type="button" class="action-btn" v-on:click="goToUserManage">
                        <i class="fas fa-users"></i>
                        <span>用户管理</span>
                    </button>
                    <button type="button" class="action-btn" v-on:click="goToItemConfig">
                        <i class="fas fa-cubes"></i>
                        <span>道具配置管理</span>
                    </button>
                    <button type="button" class="action-btn" v-on:click="goToDropConfig">
                        <i class="fas fa-gift"></i>
                        <span>掉落配置管理</span>
                    </button>
                    <button type="button" class="action-btn" v-on:click="goToUserEquipmentManage">
                        <i class="fas fa-shield-alt"></i>
                        <span>用户装备管理</span>
                    </button>
                    <button type="button" class="action-btn" v-on:click="showDevelopingAlert">
                        <i class="fas fa-chart-line"></i>
                        <span>数据统计</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="announcements-card">
                <div class="card-header-custom">
                    <div class="header-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h5>系统公告</h5>
                </div>
                <div class="announcement-item">
                    <div class="d-flex">
                        <div class="announcement-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="announcement-content">
                            <div class="announcement-title">系统提示</div>
                            <p class="announcement-text">当前系统版本 v1.0.0，功能持续完善中...</p>
                        </div>
                    </div>
                </div>
                <div class="announcement-item warning">
                    <div class="d-flex">
                        <div class="announcement-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="announcement-content">
                            <div class="announcement-title">重要提醒</div>
                            <p class="announcement-text">请定期备份重要数据，确保系统安全。</p>
                        </div>
                    </div>
                </div>
                <div class="announcement-item">
                    <div class="d-flex">
                        <div class="announcement-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="announcement-content">
                            <div class="announcement-title">功能更新</div>
                            <p class="announcement-text">新增多项管理功能，提升操作体验。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    currentUser: '@User.Identity.Name',
                    currentTime: '',
                    userCount: '...',
                    adminCount: '...',
                    todayActiveCount: '...',
                    systemStatus: '良好'
                };
            },
            methods: {
                // 更新当前时间
                updateTime() {
                    this.currentTime = new Date().toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                },
                
                // 跳转到管理员管理
                goToAdminManage() {
                    window.location.href = '@Url.Action("Index", "AdminBm")';
                },
                
                // 跳转到用户管理
                goToUserManage() {
                    window.location.href = '@Url.Action("Index", "User")';
                },
                
                // 跳转到道具配置管理
                goToItemConfig() {
                    window.location.href = '@Url.Action("Index", "ItemConfig")';
                },
                
                // 跳转到掉落配置管理
                goToDropConfig() {
                    window.location.href = '@Url.Action("Index", "DropConfig")';
                },
                
                // 跳转到用户装备管理
                goToUserEquipmentManage() {
                    window.location.href = '@Url.Action("Index", "UserEquipment")';
                },
                
                // 显示开发中提示
                showDevelopingAlert() {
                    // 使用现代化的提示
                    if (typeof toastr !== 'undefined') {
                        toastr.info('功能开发中，敬请期待！', '提示');
                    } else {
                        alert('功能开发中...');
                    }
                },
                
                // 加载统计数据
                loadStats() {
                    // 模拟加载延迟，增加真实感
                    setTimeout(() => {
                        this.userCount = '1,234';
                        this.adminCount = '5';
                        this.todayActiveCount = '89';
                    }, 800);
                }
            },
            
            mounted() {
                // 初始化时间并每秒更新
                this.updateTime();
                setInterval(this.updateTime, 1000);
                
                // 加载统计数据
                this.loadStats();

                // 为统计卡片添加延迟动画
                const cards = document.querySelectorAll('.stat-card');
                cards.forEach((card, index) => {
                    card.style.animationDelay = `${index * 0.1}s`;
                });
            }
        }).mount('#homeApp');
    </script>
}
