@{
    ViewData["Title"] = "任务配置管理";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --card-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        --card-hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        --border-radius: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modern-page {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .modern-card:hover {
        box-shadow: var(--card-hover-shadow);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 1.5rem;
    }

    .modern-card-title {
        font-weight: 600;
        font-size: 1.25rem;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1em;
    }

    .modern-btn {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }

    .modern-btn-success {
        background: var(--success-gradient);
        color: white;
    }

    .modern-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.3);
        color: white;
    }

    .modern-btn-warning {
        background: var(--warning-gradient);
        color: white;
    }

    .modern-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        color: white;
    }

    .modern-btn-info {
        background: var(--info-gradient);
        color: white;
    }

    .modern-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        color: white;
    }

    .modern-btn-danger {
        background: var(--danger-gradient);
        color: white;
    }

    .modern-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
        color: white;
    }

    .modern-form-control {
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
    }

    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        background: white;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: none;
        font-weight: 600;
        color: #475569;
        padding: 1rem;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.001);
    }

    .modern-table tbody td {
        padding: 1rem;
        border-color: #f1f5f9;
    }

    .content-header h1 {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    /* 模态框样式 */
    .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: var(--card-hover-shadow);
    }

    .modal-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .modal-title {
        font-weight: 600;
        color: #1e293b;
    }

    .form-label {
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }



    .table-sm td {
        padding: 0.5rem;
        border-color: #f1f5f9;
    }

    pre code {
        font-size: 0.875rem;
        color: #374151;
    }

    /* 任务目标配置样式 */
    .objective-item {
        background: rgba(248, 250, 252, 0.5);
        border: 1px solid #e2e8f0 !important;
        transition: var(--transition);
    }

    .objective-item:hover {
        background: rgba(248, 250, 252, 0.8);
        border-color: #cbd5e0 !important;
    }

    .objective-item .form-control-sm {
        font-size: 0.875rem;
    }

    .objective-item .form-label {
        font-size: 0.8rem;
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .objective-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.375rem;
        padding: 0.5rem 0.75rem;
        margin: -0.75rem -0.75rem 0.75rem -0.75rem;
    }

    .objective-header h6 {
        color: white !important;
        margin: 0;
    }

    .objective-count-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
</style>

<div class="modern-page">
    <!-- 任务配置管理应用容器 -->
    <div id="taskConfigApp">
        <!-- 页面标题 -->
        <div class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">
                            <i class="fas fa-tasks mr-2"></i>任务配置管理
                        </h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="/">首页</a></li>
                            <li class="breadcrumb-item active">任务配置管理</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="content">
            <div class="container-fluid">
                
                <!-- 搜索条件 -->
                <div class="card modern-card">
                    <div class="card-header modern-card-header">
                        <h3 class="card-title modern-card-title">
                            <i class="fas fa-search mr-1"></i>搜索条件
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="searchTaskName">任务名称</label>
                                    <input type="text" class="form-control modern-form-control" id="searchTaskName" 
                                           v-model="queryForm.taskName" placeholder="请输入任务名称">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="searchTaskType">任务类型</label>
                                    <select class="form-control modern-form-control" id="searchTaskType" v-model="queryForm.taskType">
                                        <option value="">全部类型</option>
                                        <option v-for="option in taskTypeOptions" 
                                                :key="option.value" :value="option.value">
                                            {{ option.label }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="searchIsActive">激活状态</label>
                                    <select class="form-control modern-form-control" id="searchIsActive" v-model="queryForm.isActive">
                                        <option value="">全部状态</option>
                                        <option value="true">已激活</option>
                                        <option value="false">已禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="button" class="btn modern-btn modern-btn-primary mr-2" v-on:click="searchTasks">
                                            <i class="fas fa-search mr-1"></i>搜索
                                        </button>
                                        <button type="button" class="btn modern-btn btn-secondary mr-2" v-on:click="resetSearch">
                                            <i class="fas fa-redo mr-1"></i>重置
                                        </button>
                                        <button type="button" class="btn modern-btn modern-btn-success" v-on:click="showAddModal">
                                            <i class="fas fa-plus mr-1"></i>新增任务
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="card modern-card">
                    <div class="card-header modern-card-header">
                        <h3 class="card-title modern-card-title">
                            <i class="fas fa-list mr-1"></i>任务配置列表
                        </h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover text-nowrap modern-table">
                            <thead>
                                <tr>
                                    <th>任务ID</th>
                                    <th>任务名称</th>
                                    <th>任务类型</th>
                                    <th>是否可重复</th>
                                    <th>前置任务</th>
                                    <th>状态</th>
                                    <th>排序</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody v-if="loading">
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <i class="fas fa-spinner fa-spin mr-2"></i>加载中...
                                    </td>
                                </tr>
                            </tbody>
                            <tbody v-else-if="tasksList.length === 0">
                                <tr>
                                    <td colspan="9" class="text-center py-4 text-muted">
                                        <i class="fas fa-inbox mr-2"></i>暂无数据
                                    </td>
                                </tr>
                            </tbody>
                            <tbody v-else>
                                <tr v-for="task in tasksList" :key="task.taskId">
                                    <td>{{ task.taskId }}</td>
                                    <td>
                                        <strong>{{ task.taskName }}</strong>
                                        <br>
                                        <small class="text-muted">{{ task.taskDescription }}</small>
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ task.taskTypeName }}</span>
                                    </td>
                                    <td>
                                        <span v-if="task.isRepeatable" class="text-muted">
                                            <i class="fas fa-check mr-1"></i>可重复
                                        </span>
                                        <span v-else class="text-muted">
                                            <i class="fas fa-times mr-1"></i>不可重复
                                        </span>
                                    </td>
                                    <td>{{ task.prerequisiteTaskName || '-' }}</td>
                                    <td>
                                        <span v-if="task.isActive" class="text-muted">
                                            <i class="fas fa-check mr-1"></i>已激活
                                        </span>
                                        <span v-else class="text-muted">
                                            <i class="fas fa-times mr-1"></i>已禁用
                                        </span>
                                    </td>
                                    <td>{{ task.sortOrder }}</td>
                                    <td>{{ formatDateTime(task.createdAt) }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-info btn-sm"
                                                    v-on:click="viewDetail(task)" title="查看详情">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button type="button" class="btn btn-warning btn-sm"
                                                    v-on:click="showEditModal(task)" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button v-if="task.isActive" type="button" class="btn btn-secondary btn-sm"
                                                    v-on:click="toggleActive(task, false)" title="禁用">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                            <button v-else type="button" class="btn btn-success btn-sm"
                                                    v-on:click="toggleActive(task, true)" title="启用">
                                                <i class="fas fa-play"></i>
                                            </button>
                                            <button type="button" class="btn btn-danger btn-sm"
                                                    v-on:click="deleteTask(task)" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="card-footer" v-if="tasksList.length > 0">
                        <div class="row align-items-center">
                            <div class="col-sm-6">
                                <div class="dataTables_info">
                                    显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条记录，共 {{ totalCount }} 条记录
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                    <ul class="pagination">
                                        <li class="paginate_button page-item previous" :class="{ disabled: currentPage === 1 }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>
                                        <li v-for="page in visiblePages" :key="page" class="paginate_button page-item"
                                            :class="{ active: page === currentPage }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                        </li>
                                        <li class="paginate_button page-item next" :class="{ disabled: currentPage === totalPages }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <!-- 任务创建/编辑模态框 -->
        <div class="modal fade" id="taskModal" tabindex="-1" aria-labelledby="taskModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="taskModalLabel">
                            <i class="fas fa-tasks mr-2"></i>{{ isEdit ? '编辑任务' : '新增任务' }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form v-on:submit.prevent="saveTask">
                            <!-- 任务ID自动生成提示 -->
                            <div class="row" v-if="isEdit">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">任务ID</label>
                                        <input type="text" class="form-control" :value="taskForm.taskId" readonly>
                                        <div class="form-text text-info">
                                            <i class="fas fa-info-circle mr-1"></i>任务ID由系统自动生成，不可修改
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="taskName" class="form-label">任务名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="taskName" v-model="taskForm.taskName"
                                               required maxlength="200">
                                    </div>
                                </div>
                            </div>

                            <!-- 创建任务时只显示任务名称 -->
                            <div class="row" v-if="!isEdit">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label for="taskName" class="form-label">任务名称 <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="taskName" v-model="taskForm.taskName"
                                               required maxlength="200">
                                        <div class="form-text text-info">
                                            <i class="fas fa-magic mr-1"></i>任务ID将在创建时自动生成
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="taskDescription" class="form-label">任务描述</label>
                                <textarea class="form-control" id="taskDescription" v-model="taskForm.taskDescription"
                                          rows="3" maxlength="500"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="taskType" class="form-label">任务类型</label>
                                        <select class="form-control" id="taskType" v-model="taskForm.taskType">
                                            <option v-for="option in taskTypeOptions"
                                                    :key="option.value" :value="parseInt(option.value)">
                                                {{ option.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="prerequisiteTask" class="form-label">前置任务</label>
                                        <select class="form-control" id="prerequisiteTask" v-model="taskForm.prerequisiteTask">
                                            <option value="">无前置任务</option>
                                            <option v-for="option in taskOptions"
                                                    :key="option.value" :value="option.value">
                                                {{ option.label }}
                                            </option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="sortOrder" class="form-label">排序顺序</label>
                                        <input type="number" class="form-control" id="sortOrder" v-model="taskForm.sortOrder"
                                               min="0" max="9999">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isRepeatable"
                                                   v-model="taskForm.isRepeatable">
                                            <label class="form-check-label" for="isRepeatable">
                                                可重复任务
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="isActive"
                                                   v-model="taskForm.isActive">
                                            <label class="form-check-label" for="isActive">
                                                启用任务
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="rewardConfig" class="form-label">奖励配置 (JSON格式)</label>
                                <textarea class="form-control" id="rewardConfig" v-model="taskForm.rewardConfig"
                                          rows="3" placeholder='{"exp": 100, "gold": 50}'></textarea>
                                <div class="form-text">JSON格式的奖励配置，例如：{"exp": 100, "gold": 50}</div>
                            </div>

                            <!-- 任务目标配置 -->
                            <div class="mb-3">
                                <label class="form-label">任务目标配置</label>
                                <div class="border rounded p-3">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h6 class="mb-0">
                                            <i class="fas fa-bullseye mr-1"></i>目标列表
                                            <span class="badge objective-count-badge ml-2" v-if="taskForm.objectives.length > 0">
                                                {{ taskForm.objectives.length }} 个目标
                                            </span>
                                        </h6>
                                        <button type="button" class="btn btn-sm btn-outline-primary" v-on:click="addObjective">
                                            <i class="fas fa-plus mr-1"></i>添加目标
                                        </button>
                                    </div>

                                    <div v-if="taskForm.objectives.length === 0" class="text-center text-muted py-3">
                                        <i class="fas fa-inbox mr-2"></i>暂无目标，点击"添加目标"按钮添加
                                    </div>

                                    <div v-for="(objective, index) in taskForm.objectives" :key="index"
                                         class="objective-item border rounded p-3 mb-2">
                                        <div class="objective-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-target mr-1"></i>目标 {{ index + 1 }}
                                            </h6>
                                            <button type="button" class="btn btn-sm btn-outline-light"
                                                    v-on:click="removeObjective(index)" title="删除此目标">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">目标类型 <span class="text-danger">*</span></label>
                                                <select class="form-control form-control-sm" v-model="objective.objectiveType" required>
                                                    <option value="">请选择目标类型</option>
                                                    <option v-for="option in objectiveTypeOptions"
                                                            :key="option.value" :value="option.value">
                                                        {{ option.label }}
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">目标ID</label>
                                                <input type="text" class="form-control form-control-sm"
                                                       v-model="objective.targetId" placeholder="怪物ID/道具ID等">
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">目标数量 <span class="text-danger">*</span></label>
                                                <input type="number" class="form-control form-control-sm"
                                                       v-model="objective.targetAmount" min="1" required>
                                            </div>
                                            <div class="col-md-2">
                                                <label class="form-label">顺序</label>
                                                <input type="number" class="form-control form-control-sm"
                                                       v-model="objective.objectiveOrder" min="0">
                                            </div>

                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-12">
                                                <label class="form-label">目标描述</label>
                                                <input type="text" class="form-control form-control-sm"
                                                       v-model="objective.objectiveDescription"
                                                       placeholder="目标的详细描述">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times mr-1"></i>取消
                        </button>
                        <button type="button" class="btn btn-primary" v-on:click="saveTask" :disabled="saving">
                            <i class="fas fa-spinner fa-spin mr-1" v-if="saving"></i>
                            <i class="fas fa-save mr-1" v-else></i>
                            {{ saving ? '保存中...' : (isEdit ? '更新' : '创建') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务详情模态框 -->
        <div class="modal fade" id="taskDetailModal" tabindex="-1" aria-labelledby="taskDetailModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="taskDetailModalLabel">
                            <i class="fas fa-info-circle mr-2"></i>任务详情
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" v-if="selectedTask">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-id-card mr-1"></i>基本信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>任务ID:</strong></td>
                                        <td>{{ selectedTask.taskId }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>任务名称:</strong></td>
                                        <td>{{ selectedTask.taskName }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>任务类型:</strong></td>
                                        <td><span class="text-muted">{{ selectedTask.taskTypeName }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>状态:</strong></td>
                                        <td>
                                            <span v-if="selectedTask.isActive" class="text-muted">已启用</span>
                                            <span v-else class="text-muted">已禁用</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>可重复:</strong></td>
                                        <td>
                                            <span v-if="selectedTask.isRepeatable" class="text-muted">是</span>
                                            <span v-else class="text-muted">否</span>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-cog mr-1"></i>配置信息</h6>
                                <table class="table table-sm">
                                    <tr>
                                        <td><strong>前置任务:</strong></td>
                                        <td>{{ selectedTask.prerequisiteTaskName || '无' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>排序顺序:</strong></td>
                                        <td>{{ selectedTask.sortOrder }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>创建时间:</strong></td>
                                        <td>{{ formatDateTime(selectedTask.createdAt) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>更新时间:</strong></td>
                                        <td>{{ formatDateTime(selectedTask.updatedAt) }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <div class="mt-3" v-if="selectedTask.taskDescription">
                            <h6><i class="fas fa-align-left mr-1"></i>任务描述</h6>
                            <p class="text-muted">{{ selectedTask.taskDescription }}</p>
                        </div>

                        <div class="mt-3" v-if="selectedTask.rewardConfig">
                            <h6><i class="fas fa-gift mr-1"></i>奖励配置</h6>
                            <pre class="bg-light p-2 rounded"><code>{{ selectedTask.rewardConfig }}</code></pre>
                        </div>

                        <div class="mt-3" v-if="selectedTask.objectives && selectedTask.objectives.length > 0">
                            <h6><i class="fas fa-bullseye mr-1"></i>任务目标</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>顺序</th>
                                            <th>目标类型</th>
                                            <th>目标ID</th>
                                            <th>目标数量</th>
                                            <th>描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="objective in selectedTask.objectives" :key="objective.objectiveId">
                                            <td>{{ objective.objectiveOrder }}</td>
                                            <td><span class="text-muted">{{ objective.objectiveTypeName }}</span></td>
                                            <td>{{ objective.targetId || '-' }}</td>
                                            <td>{{ objective.targetAmount }}</td>
                                            <td>{{ objective.objectiveDescription || '-' }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times mr-1"></i>关闭
                        </button>
                        <button type="button" class="btn btn-warning" v-on:click="editTaskFromDetail" v-if="selectedTask">
                            <i class="fas fa-edit mr-1"></i>编辑任务
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 任务配置管理Vue应用
        Vue.createApp({
            data() {
                return {
                    // 加载状态
                    loading: false,
                    saving: false,

                    // 任务列表数据
                    tasksList: [],
                    totalCount: 0,
                    currentPage: 1,
                    pageSize: 10,

                    // 表单状态
                    isEdit: false,
                    selectedTask: null,

                    // 下拉选项
                    taskTypeOptions: [],
                    taskOptions: [],
                    objectiveTypeOptions: [],

                    // 查询表单
                    queryForm: {
                        taskName: '',
                        taskType: '',
                        isActive: ''
                    },

                    // 任务表单
                    taskForm: {
                        taskId: '',
                        taskName: '',
                        taskDescription: '',
                        taskType: 0,
                        isRepeatable: false,
                        prerequisiteTask: '',
                        requiredPet: '',
                        rewardConfig: '',
                        isNetworkTask: false,
                        isActive: true,
                        sortOrder: 0,
                        objectives: []
                    }
                };
            },

            computed: {
                // 总页数
                totalPages() {
                    return Math.ceil(this.totalCount / this.pageSize);
                },

                // 可见页码
                visiblePages() {
                    const total = this.totalPages;
                    const current = this.currentPage;
                    const delta = 2;
                    const pages = [];

                    for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                        pages.push(i);
                    }

                    if (current - delta > 2) {
                        pages.unshift("...");
                    }
                    if (current + delta < total - 1) {
                        pages.push("...");
                    }

                    pages.unshift(1);
                    if (total > 1) {
                        pages.push(total);
                    }

                    return pages.filter((page, index, arr) => arr.indexOf(page) === index);
                }
            },

            methods: {
                // 加载任务列表
                async loadTasks() {
                    try {
                        this.loading = true;

                        const requestData = {
                            TaskName: this.queryForm.taskName || '',
                            TaskType: this.queryForm.taskType ? parseInt(this.queryForm.taskType) : null,
                            IsActive: this.queryForm.isActive === '' ? null : (this.queryForm.isActive === 'true'),
                            Page: this.currentPage,
                            PageSize: this.pageSize
                        };

                        console.log('发送请求数据:', requestData);

                        const response = await axios.post('/TaskConfig/GetList', requestData);
                        if (response.data.code === 200) {
                            this.tasksList = response.data.data || [];
                            this.totalCount = response.data.total || 0;
                        } else {
                            console.error('获取任务列表失败：', response.data.message);
                            alert('获取任务列表失败：' + response.data.message);
                        }
                    } catch (error) {
                        console.error('获取任务列表失败：', error);
                        alert('获取任务列表失败，请重试');
                    } finally {
                        this.loading = false;
                    }
                },

                // 搜索任务
                searchTasks() {
                    console.log('搜索参数:', this.queryForm);
                    this.currentPage = 1;
                    this.loadTasks();
                },

                // 重置搜索
                resetSearch() {
                    Object.assign(this.queryForm, {
                        taskName: '',
                        taskType: '',
                        isActive: ''
                    });
                    this.searchTasks();
                },

                // 分页
                changePage(page) {
                    if (page >= 1 && page <= this.totalPages) {
                        this.currentPage = page;
                        this.loadTasks();
                    }
                },

                // 显示新增模态框
                showAddModal() {
                    this.isEdit = false;
                    this.taskForm = {
                        taskName: '',
                        taskDescription: '',
                        taskType: 0,
                        isRepeatable: false,
                        prerequisiteTask: '',
                        requiredPet: '',
                        rewardConfig: '',
                        isNetworkTask: false,
                        isActive: true,
                        sortOrder: 0,
                        objectives: []
                    };

                    // 使用Bootstrap 5的方式显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
                    modal.show();
                },

                // 显示编辑模态框
                showEditModal(task) {
                    this.isEdit = true;
                    this.taskForm = {
                        taskId: task.taskId,
                        taskName: task.taskName,
                        taskDescription: task.taskDescription,
                        taskType: task.taskType,
                        isRepeatable: task.isRepeatable,
                        prerequisiteTask: task.prerequisiteTask,
                        requiredPet: task.requiredPet,
                        rewardConfig: task.rewardConfig,
                        isNetworkTask: task.isNetworkTask,
                        isActive: task.isActive,
                        sortOrder: task.sortOrder,
                        objectives: task.objectives || []
                    };

                    // 使用Bootstrap 5的方式显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('taskModal'));
                    modal.show();
                },

                // 保存任务
                async saveTask() {
                    try {
                        this.saving = true;

                        // 验证必填字段
                        if (!this.taskForm.taskName.trim()) {
                            alert('请输入任务名称');
                            return;
                        }

                        const url = this.isEdit ? '/TaskConfig/Update' : '/TaskConfig/Create';
                        const response = await axios.post(url, this.taskForm);

                        if (response.data.code === 200) {
                            // 关闭模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
                            modal.hide();

                            // 刷新列表
                            await this.loadTasks();

                            alert(response.data.message || (this.isEdit ? '更新成功' : '创建成功'));
                        } else {
                            alert(response.data.message || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存任务失败：', error);
                        alert('保存失败，请重试');
                    } finally {
                        this.saving = false;
                    }
                },

                // 删除任务
                async deleteTask(task) {
                    if (!confirm(`确定要删除任务"${task.taskName}"吗？此操作无法撤销！`)) {
                        return;
                    }

                    try {
                        const response = await axios.post('/TaskConfig/Delete', { taskId: task.taskId });
                        if (response.data.code === 200) {
                            this.loadTasks();
                            alert('删除成功');
                        } else {
                            alert(response.data.message || '删除失败');
                        }
                    } catch (error) {
                        console.error('删除任务失败：', error);
                        alert('删除失败，请重试');
                    }
                },

                // 切换任务状态
                async toggleActive(task, isActive) {
                    try {
                        const response = await axios.post('/TaskConfig/ToggleActive', {
                            taskId: task.taskId,
                            isActive: isActive
                        });

                        if (response.data.code === 200) {
                            this.loadTasks();
                            alert(response.data.message || '状态切换成功');
                        } else {
                            alert(response.data.message || '状态切换失败');
                        }
                    } catch (error) {
                        console.error('切换任务状态失败：', error);
                        alert('状态切换失败，请重试');
                    }
                },

                // 查看详情
                async viewDetail(task) {
                    try {
                        const response = await axios.get(`/TaskConfig/GetById/${task.taskId}`);
                        if (response.data.code === 200) {
                            this.selectedTask = response.data.data;

                            // 使用Bootstrap 5的方式显示模态框
                            const modal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
                            modal.show();
                        } else {
                            alert(response.data.message || '获取任务详情失败');
                        }
                    } catch (error) {
                        console.error('获取任务详情失败：', error);
                        alert('获取任务详情失败，请重试');
                    }
                },

                // 从详情页面编辑任务
                editTaskFromDetail() {
                    // 关闭详情模态框
                    const detailModal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
                    detailModal.hide();

                    // 显示编辑模态框
                    setTimeout(() => {
                        this.showEditModal(this.selectedTask);
                    }, 300);
                },

                // 添加任务目标
                addObjective() {
                    this.taskForm.objectives.push({
                        objectiveType: '',
                        targetId: '',
                        targetAmount: 1,
                        objectiveOrder: this.taskForm.objectives.length,
                        objectiveDescription: ''
                    });
                },

                // 删除任务目标
                removeObjective(index) {
                    if (confirm('确定要删除这个目标吗？')) {
                        this.taskForm.objectives.splice(index, 1);
                        // 重新排序
                        this.taskForm.objectives.forEach((obj, idx) => {
                            obj.objectiveOrder = idx;
                        });
                    }
                },

                // 格式化日期时间
                formatDateTime(dateStr) {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return date.toLocaleString('zh-CN');
                },

                // 加载下拉选项
                async loadOptions() {
                    try {
                        // 加载任务类型选项
                        const typeResponse = await axios.get('/TaskConfig/GetTaskTypeOptions');
                        if (typeResponse.data.code === 200) {
                            this.taskTypeOptions = typeResponse.data.data;
                        }

                        // 加载任务选项
                        const taskResponse = await axios.get('/TaskConfig/GetTaskOptions');
                        if (taskResponse.data.code === 200) {
                            this.taskOptions = taskResponse.data.data;
                        }

                        // 加载目标类型选项
                        this.objectiveTypeOptions = [
                            { value: 'KILL_MONSTER', label: '击杀怪物' },
                            { value: 'COLLECT_ITEM', label: '收集道具' },
                            { value: 'REACH_LEVEL', label: '达到等级' },
                            { value: 'VISIT_MAP', label: '访问地图' },
                            { value: 'USE_SKILL', label: '使用技能' },
                            { value: 'EQUIP_ITEM', label: '装备道具' },
                            { value: 'COMPLETE_DUNGEON', label: '完成副本' },
                            { value: 'PET_EVOLUTION', label: '宠物进化' },
                            { value: 'ENHANCE_EQUIPMENT', label: '强化装备' },
                            { value: 'TALK_TO_NPC', label: '与NPC对话' }
                        ];
                    } catch (error) {
                        console.error('加载选项失败：', error);
                    }
                }
            },

            async mounted() {
                await this.loadOptions();
                await this.loadTasks();
            }
        }).mount('#taskConfigApp');
    </script>
}
