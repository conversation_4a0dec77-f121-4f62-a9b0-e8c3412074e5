{"Version": 1, "WorkspaceRootPath": "D:\\AI OB\\HM_one_bg\\BMS\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\taskconfigcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\taskconfigcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\views\\taskconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:views\\taskconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\useritemservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\useritemservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\useritemcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\useritemcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\userequipmentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\userequipmentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\userequipmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\userequipmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\dtos\\userequipmentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\dtos\\userequipmentdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\userpetservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\userpetservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\entities\\item_config.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\entities\\item_config.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\itemconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\itemconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\class.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\class.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\entities\\drop_config.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\entities\\drop_config.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\dropconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\dropconfigservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\services\\implementations\\equipmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:services\\implementations\\equipmentservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\itemconfigcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\itemconfigcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\views\\itemconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:views\\itemconfig\\index.cshtml||{40D31677-CBC0-4297-A9EF-89D907823A98}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\models\\entities\\equipment.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:models\\entities\\equipment.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|d:\\ai ob\\hm_one_bg\\bms\\controllers\\equipmentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{75205788-6466-48DB-AC86-28EB0CF49CFC}|BMS.csproj|solutionrelative:controllers\\equipmentcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "TaskConfigController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\TaskConfigController.cs", "RelativeDocumentMoniker": "Controllers\\TaskConfigController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\TaskConfigController.cs", "RelativeToolTip": "Controllers\\TaskConfigController.cs", "ViewState": "AgIAAHgAAAAAAAAAAAAAAIQAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-26T16:13:39.77Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Index.cshtml", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\TaskConfig\\Index.cshtml", "RelativeDocumentMoniker": "Views\\TaskConfig\\Index.cshtml", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\TaskConfig\\Index.cshtml", "RelativeToolTip": "Views\\TaskConfig\\Index.cshtml", "ViewState": "AgIAACcAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-08-26T14:12:02.177Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "UserItemService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserItemService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\UserItemService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserItemService.cs", "RelativeToolTip": "Services\\Implementations\\UserItemService.cs", "ViewState": "AgIAAG8AAAAAAAAAAAAhwJQAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T14:45:02.004Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UserItemController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserItemController.cs", "RelativeDocumentMoniker": "Controllers\\UserItemController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserItemController.cs", "RelativeToolTip": "Controllers\\UserItemController.cs", "ViewState": "AgIAADQAAAAAAAAAAADwv1cAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-12T14:44:27.549Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "UserEquipmentController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserEquipmentController.cs", "RelativeDocumentMoniker": "Controllers\\UserEquipmentController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\UserEquipmentController.cs", "RelativeToolTip": "Controllers\\UserEquipmentController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACgAAABaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-08T03:55:44.409Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "UserEquipmentDto.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\DTOs\\UserEquipmentDto.cs", "RelativeDocumentMoniker": "Models\\DTOs\\UserEquipmentDto.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\DTOs\\UserEquipmentDto.cs", "RelativeToolTip": "Models\\DTOs\\UserEquipmentDto.cs", "ViewState": "AgIAAKcAAAAAAAAAAAAWwLQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T12:14:53.27Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "UserEquipmentService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserEquipmentService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\UserEquipmentService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserEquipmentService.cs", "RelativeToolTip": "Services\\Implementations\\UserEquipmentService.cs", "ViewState": "AgIAACEAAAAAAAAAAABQwDsAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T09:01:38.023Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "UserPetService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserPetService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\UserPetService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\UserPetService.cs", "RelativeToolTip": "Services\\Implementations\\UserPetService.cs", "ViewState": "AgIAAAMAAAAAAAAAAADwvwwAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-08T02:51:05.819Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "item_config.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\item_config.cs", "RelativeDocumentMoniker": "Models\\Entities\\item_config.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\item_config.cs", "RelativeToolTip": "Models\\Entities\\item_config.cs", "ViewState": "AgIAAEAAAAAAAAAAAABAwG8AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-08T02:30:24.829Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ItemConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\ItemConfigService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\ItemConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\ItemConfigService.cs", "RelativeToolTip": "Services\\Implementations\\ItemConfigService.cs", "ViewState": "AgIAACgCAAAAAAAAAAAIwDkCAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T08:04:00.377Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "appsettings.json", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\appsettings.json", "RelativeDocumentMoniker": "appsettings.json", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\appsettings.json", "RelativeToolTip": "appsettings.json", "ViewState": "AgIAAAMAAAAAAAAAAABIwAAAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-08-01T06:39:18.66Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "Class.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Class.cs", "RelativeDocumentMoniker": "Models\\Class.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Class.cs", "RelativeToolTip": "Models\\Class.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-08T01:38:17.397Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "drop_config.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\drop_config.cs", "RelativeDocumentMoniker": "Models\\Entities\\drop_config.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\drop_config.cs", "RelativeToolTip": "Models\\Entities\\drop_config.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-08T01:38:00.238Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "ItemConfigController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\ItemConfigController.cs", "RelativeDocumentMoniker": "Controllers\\ItemConfigController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\ItemConfigController.cs", "RelativeToolTip": "Controllers\\ItemConfigController.cs", "ViewState": "AgIAAPAAAAAAAAAAAABAwAQBAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T08:03:13.669Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "DropConfigService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\DropConfigService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\DropConfigService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\DropConfigService.cs", "RelativeToolTip": "Services\\Implementations\\DropConfigService.cs", "ViewState": "AgIAAFwAAAAAAAAAAADgv2sAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T08:58:41.244Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "Index.cshtml", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\ItemConfig\\Index.cshtml", "RelativeDocumentMoniker": "Views\\ItemConfig\\Index.cshtml", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Views\\ItemConfig\\Index.cshtml", "RelativeToolTip": "Views\\ItemConfig\\Index.cshtml", "ViewState": "AgIAACMBAAAAAAAAAADgvzUBAAA3AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000759|", "WhenOpened": "2025-07-31T07:29:12.207Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "equipment.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\equipment.cs", "RelativeDocumentMoniker": "Models\\Entities\\equipment.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Models\\Entities\\equipment.cs", "RelativeToolTip": "Models\\Entities\\equipment.cs", "ViewState": "AgIAAA0AAAAAAAAAAABAwB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:38:18.543Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "EquipmentService.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\EquipmentService.cs", "RelativeDocumentMoniker": "Services\\Implementations\\EquipmentService.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Services\\Implementations\\EquipmentService.cs", "RelativeToolTip": "Services\\Implementations\\EquipmentService.cs", "ViewState": "AgIAAAoAAAAAAAAAAABAwAkAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:36:00.345Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "EquipmentController.cs", "DocumentMoniker": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\EquipmentController.cs", "RelativeDocumentMoniker": "Controllers\\EquipmentController.cs", "ToolTip": "D:\\AI OB\\HM_one_bg\\BMS\\Controllers\\EquipmentController.cs", "RelativeToolTip": "Controllers\\EquipmentController.cs", "ViewState": "AgIAAB4AAAAAAAAAAADwv0sAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T06:34:45.161Z", "EditorCaption": ""}]}]}]}