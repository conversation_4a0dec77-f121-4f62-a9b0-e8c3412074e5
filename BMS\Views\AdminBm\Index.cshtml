@{
    ViewData["Title"] = "管理员管理";
}

<div id="adminApp">
    <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>管理员管理</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">首页</a></li>
                            <li class="breadcrumb-item active">管理员管理</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">管理员列表</h3>
                                <div class="card-tools">
                                    <a href="@Url.Action("Create")" class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus"></i> 新增管理员
                                    </a>
                                </div>
                            </div>

                            <!-- 搜索表单 -->
                            <div class="card-body">
                                @Html.AntiForgeryToken()
                                <form v-on:submit.prevent="searchData" class="mb-3">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <input type="text" v-model="searchForm.username" 
                                                   class="form-control" placeholder="用户名">
                                        </div>
                                        <div class="col-md-3">
                                            <input type="text" v-model="searchForm.realName" 
                                                   class="form-control" placeholder="真实姓名">
                                        </div>
                                        <div class="col-md-2">
                                            <select v-model="searchForm.status" class="form-control">
                                                <option value="">全部状态</option>
                                                <option value="1">启用</option>
                                                <option value="0">禁用</option>
                                            </select>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="submit" class="btn btn-info">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" v-on:click="resetSearch" class="btn btn-secondary">
                                                <i class="fas fa-redo"></i> 重置
                                            </button>
                                        </div>
                                    </div>
                                </form>

                                <!-- 加载状态 -->
                                <div v-if="loading" class="text-center py-3">
                                    <i class="fas fa-spinner fa-spin"></i> 数据加载中...
                                </div>

                                <!-- 数据表格 -->
                                <div v-else class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>用户名</th>
                                                <th>真实姓名</th>
                                                <th>手机号</th>
                                                <th>邮箱</th>
                                                <th>状态</th>
                                                <th>创建时间</th>
                                                <th>最后登录</th>
                                                <th width="200">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-if="adminList.length === 0">
                                                <td colspan="9" class="text-center">暂无数据</td>
                                            </tr>
                                            <tr v-for="admin in adminList" :key="admin.id">
                                                <td>{{ admin.id }}</td>
                                                <td>{{ admin.username }}</td>
                                                <td>{{ admin.realName || '-' }}</td>
                                                <td>{{ admin.phone || '-' }}</td>
                                                <td>{{ admin.email || '-' }}</td>
                                                <td>
                                                    <span v-if="admin.status === 1" class="badge-success">启用</span>
                                                    <span v-else class="badge-danger">禁用</span>
                                                </td>
                                                <td>{{ formatDate(admin.createTime) }}</td>
                                                <td>{{ admin.lastLoginTime ? formatDate(admin.lastLoginTime) : '-' }}</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a :href="'/AdminBm/Details?id=' + admin.id" 
                                                           class="btn btn-info btn-sm" title="查看详情">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a :href="'/AdminBm/Edit?id=' + admin.id" 
                                                           class="btn btn-warning btn-sm" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <button type="button" 
                                                                :class="'btn btn-' + (admin.status === 1 ? 'secondary' : 'success') + ' btn-sm'" 
                                                                v-on:click="changeStatus(admin.id, admin.status === 1 ? 0 : 1)" 
                                                                :title="admin.status === 1 ? '禁用' : '启用'">
                                                            <i :class="'fas fa-' + (admin.status === 1 ? 'ban' : 'check')"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-primary btn-sm" 
                                                                v-on:click="openResetPasswordModal(admin.id)" title="重置密码">
                                                            <i class="fas fa-key"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-danger btn-sm" 
                                                                v-on:click="deleteAdmin(admin.id)" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <nav v-if="totalPages > 1" aria-label="分页导航">
                                    <ul class="pagination justify-content-center">
                                        <!-- 上一页 -->
                                        <li :class="`page-item ${currentPage === 1 ? 'disabled' : ''}`">
                                            <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>

                                        <!-- 页码 -->
                                        <li v-for="page in visiblePages" :key="page" 
                                            :class="`page-item ${page === currentPage ? 'active' : ''}`">
                                            <a class="page-link" href="#" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                        </li>

                                        <!-- 下一页 -->
                                        <li :class="`page-item ${currentPage === totalPages ? 'disabled' : ''}`">
                                            <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                </nav>

                                <div v-if="totalPages > 1" class="text-center">
                                    <small class="text-muted">
                                        共 {{ totalCount }} 条记录，第 {{ currentPage }}/{{ totalPages }} 页
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 重置密码模态框 -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1" ref="resetPasswordModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">重置密码</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>新密码：</label>
                        <input type="password" v-model="resetPasswordForm.newPassword" 
                               class="form-control" placeholder="请输入新密码（至少6位）">
                    </div>
                    <div class="form-group">
                        <label>确认密码：</label>
                        <input type="password" v-model="resetPasswordForm.confirmPassword" 
                               class="form-control" placeholder="请再次输入新密码">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" v-on:click="confirmResetPassword" 
                            :disabled="resetPasswordLoading">
                        {{ resetPasswordLoading ? '重置中...' : '确认重置' }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <!-- 引入本地 Axios 库 -->
    <script src="~/lib/axios.min.js"></script>
    
    <script>
        // 配置axios默认请求头
        axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
        
        // 初始化Vue管理员管理应用
        Vue.createApp({
            data() {
                return {
                    // 搜索表单
                    searchForm: {
                        username: '',
                        realName: '',
                        status: ''
                    },
                    // 数据列表
                    adminList: [],
                    // 分页信息
                    currentPage: 1,
                    pageSize: 10,
                    totalCount: 0,
                    totalPages: 0,
                    // 加载状态
                    loading: false,
                    // 重置密码
                    currentAdminId: 0,
                    resetPasswordForm: {
                        newPassword: '',
                        confirmPassword: ''
                    },
                    resetPasswordLoading: false
                };
            },
            computed: {
                // 可见的页码
                visiblePages() {
                    const pages = [];
                    const start = Math.max(1, this.currentPage - 2);
                    const end = Math.min(this.totalPages, this.currentPage + 2);
                    for (let i = start; i <= end; i++) {
                        pages.push(i);
                    }
                    return pages;
                }
            },
            methods: {
                // 加载数据
                async loadData() {
                    this.loading = true;
                    console.log('开始加载数据...');
                    console.log('当前搜索条件:', this.searchForm);
                    
                    const params = {
                        PageIndex: this.currentPage,
                        PageSize: this.pageSize
                    };
                    
                    // 只添加有值的参数
                    if (this.searchForm.username) {
                        params.Username = this.searchForm.username;
                    }
                    if (this.searchForm.realName) {
                        params.RealName = this.searchForm.realName;
                    }
                    if (this.searchForm.status !== '') {
                        params.Status = this.searchForm.status;
                    }
                    
                    console.log('请求参数:', params);
                    
                    try {
                        const response = await axios.get('/AdminBm/GetList', { params: params });
                        console.log('完整响应:', response);
                        console.log('响应状态:', response.status);
                        console.log('响应数据:', response.data);
                        
                        if (response.data.success && response.data.data) {
                            const result = response.data.data;
                            console.log('解析结果:', result);
                            console.log('管理员列表:', result.data);
                            
                            this.adminList = result.data || [];
                            this.currentPage = result.pageIndex || 1;
                            this.totalCount = result.totalCount || 0;
                            this.totalPages = result.totalPages || 0;
                            
                            console.log('Vue数据更新后:');
                            console.log('adminList:', this.adminList);
                            console.log('totalCount:', this.totalCount);
                            console.log('currentPage:', this.currentPage);
                            console.log('totalPages:', this.totalPages);
                        } else {
                            console.error('接口返回失败:', response.data);
                            this.showError(response.data.message || '获取数据失败');
                        }
                    } catch (error) {
                        console.error('请求失败:', error);
                        console.error('错误详情:', error.response);
                        
                        if (error.response) {
                            // 请求已发出，但服务器响应了错误状态码
                            console.error('响应状态:', error.response.status);
                            console.error('响应数据:', error.response.data);
                            this.showError(`服务器错误 (状态码: ${error.response.status})`);
                        } else if (error.request) {
                            // 请求已发出，但没有收到响应
                            console.error('无响应:', error.request);
                            this.showError('网络连接错误，请检查网络');
                        } else {
                            // 其他错误
                            console.error('错误信息:', error.message);
                            this.showError(`请求失败: ${error.message}`);
                        }
                    } finally {
                        this.loading = false;
                        console.log('数据加载完成，loading状态:', this.loading);
                    }
                },
                
                // 搜索数据
                searchData() {
                    console.log('执行搜索，重置到第1页');
                    this.currentPage = 1;
                    this.loadData();
                },
                
                // 重置搜索
                resetSearch() {
                    console.log('重置搜索条件');
                    this.searchForm = {
                        username: '',
                        realName: '',
                        status: ''
                    };
                    this.currentPage = 1;
                    this.loadData();
                },
                
                // 切换页码
                changePage(page) {
                    if (page < 1 || page > this.totalPages || page === this.currentPage) {
                        return;
                    }
                    console.log(`切换到第${page}页`);
                    this.currentPage = page;
                    this.loadData();
                },
                
                // 删除管理员
                async deleteAdmin(id) {
                    if (!confirm('确定要删除这个管理员吗？')) {
                        return;
                    }
                    
                    console.log('删除管理员ID:', id);
                    
                    try {
                        // 使用JSON格式发送POST请求
                        const response = await axios.post('/AdminBm/Delete', { id }, {
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            }
                        });
                        console.log('删除响应:', response.data);
                        if (response.data.success) {
                            this.showSuccess(response.data.message);
                            this.loadData();
                        } else {
                            this.showError(response.data.message);
                        }
                    } catch (error) {
                        console.error('删除失败:', error);
                        this.showError('删除失败，请稍后重试');
                    }
                },
                
                // 更改状态
                async changeStatus(id, status) {
                    const action = status === 1 ? '启用' : '禁用';
                    if (!confirm(`确定要${action}这个管理员吗？`)) {
                        return;
                    }
                    
                    console.log('更改状态 - ID:', id, 'Status:', status);
                    
                    try {
                        // 使用JSON格式发送POST请求
                        const response = await axios.post('/AdminBm/ChangeStatus', { id, status }, {
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            }
                        });
                        console.log('状态更改响应:', response.data);
                        if (response.data.success) {
                            this.showSuccess(response.data.message);
                            this.loadData();
                        } else {
                            this.showError(response.data.message);
                        }
                    } catch (error) {
                        console.error('状态更改失败:', error);
                        this.showError('操作失败，请稍后重试');
                    }
                },
                
                // 打开重置密码模态框
                openResetPasswordModal(id) {
                    console.log('打开重置密码模态框 - ID:', id);
                    this.currentAdminId = id;
                    this.resetPasswordForm = {
                        newPassword: '',
                        confirmPassword: ''
                    };
                    // 使用Bootstrap 5的方式显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
                    modal.show();
                },
                
                // 确认重置密码
                async confirmResetPassword() {
                    const newPassword = this.resetPasswordForm.newPassword;
                    const confirmPassword = this.resetPasswordForm.confirmPassword;
                    
                    if (!newPassword) {
                        this.showError('请输入新密码');
                        return;
                    }
                    
                    if (newPassword.length < 6) {
                        this.showError('密码长度不能少于6位');
                        return;
                    }
                    
                    if (newPassword !== confirmPassword) {
                        this.showError('两次输入的密码不一致');
                        return;
                    }
                    
                    console.log('重置密码 - ID:', this.currentAdminId);
                    
                    this.resetPasswordLoading = true;
                    
                    try {
                        const response = await axios.post('/AdminBm/ResetPassword', {
                            id: this.currentAdminId,
                            newPassword: newPassword
                        }, {
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            }
                        });
                        
                        console.log('重置密码响应:', response.data);
                        if (response.data.success) {
                            this.showSuccess(response.data.message);
                            // 使用Bootstrap 5的方式隐藏模态框
                            const modal = bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal'));
                            modal.hide();
                        } else {
                            this.showError(response.data.message);
                        }
                    } catch (error) {
                        console.error('重置密码失败:', error);
                        this.showError('重置密码失败，请稍后重试');
                    } finally {
                        this.resetPasswordLoading = false;
                    }
                },
                
                // 格式化日期
                formatDate(dateStr) {
                    if (!dateStr) return '-';
                    const date = new Date(dateStr);
                    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
                        hour12: false, 
                        hour: '2-digit', 
                        minute: '2-digit' 
                    });
                },
                
                // 显示成功消息
                showSuccess(message) {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(message);
                    } else {
                        alert(message);
                    }
                },
                
                // 显示错误消息
                showError(message) {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(message);
                    } else {
                        alert(message);
                    }
                }
            },
            
            mounted() {
                console.log('Vue应用已挂载，准备加载数据');
                // 页面加载时获取数据
                this.loadData();
            }
        }).mount('#adminApp');
    </script>
} 