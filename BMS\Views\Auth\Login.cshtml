@{
    Layout = null;
    ViewData["Title"] = "登录";
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>@ViewData["Title"] - 后台管理系统</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <!-- Toastr -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --card-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
            --input-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            position: relative;
            overflow: hidden;
        }

        @@keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 动态背景粒子效果 */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 20s infinite linear;
        }

        @@keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: var(--card-shadow);
            width: 100%;
            max-width: 440px;
            padding: 50px 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideUp 0.8s ease-out;
        }

        @@keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .login-title {
            font-size: 32px;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }

        .login-subtitle {
            color: #64748b;
            font-size: 16px;
            font-weight: 400;
            margin: 0;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 16px 50px 16px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 400;
            transition: var(--transition);
            background: #fff;
            color: #1e293b;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-control::placeholder {
            color: #94a3b8;
            font-weight: 400;
        }

        .input-icon {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #94a3b8;
            font-size: 18px;
            pointer-events: none;
            transition: var(--transition);
        }

        .form-control:focus + .input-icon {
            color: #667eea;
        }

        .remember-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 32px;
        }

        .custom-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            user-select: none;
        }

        .custom-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            cursor: pointer;
            transition: var(--transition);
        }

        .custom-checkbox input[type="checkbox"]:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .custom-checkbox label {
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            margin: 0;
        }

        .forgot-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
        }

        .forgot-link:hover {
            color: #5a67d8;
            text-decoration: none;
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: var(--primary-gradient);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .login-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .login-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: relative;
            z-index: 1;
        }

        .btn-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
            border: 1px solid #fca5a5;
            color: #dc2626;
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: 500;
            animation: shake 0.6s ease-in-out;
        }

        @@keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            animation: fadeIn 0.3s ease-out forwards;
        }

        @@keyframes fadeIn {
            to { opacity: 1; }
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* 响应式设计 */
        @@media (max-width: 480px) {
            .login-card {
                margin: 20px;
                padding: 40px 30px;
                border-radius: 20px;
            }

            .login-title {
                font-size: 28px;
            }

            .form-control {
                padding: 14px 45px 14px 16px;
                font-size: 16px;
            }

            .login-btn {
                padding: 14px;
                font-size: 16px;
            }
        }

        /* 深色模式支持 */
        @@media (prefers-color-scheme: dark) {
            .login-card {
                background: rgba(30, 41, 59, 0.95);
                border-color: rgba(255, 255, 255, 0.1);
            }

            .login-subtitle {
                color: #94a3b8;
            }

            .form-control {
                background: rgba(51, 65, 85, 0.5);
                border-color: #334155;
                color: #f1f5f9;
            }

            .form-control::placeholder {
                color: #64748b;
            }

            .custom-checkbox label {
                color: #94a3b8;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>

    <div id="loginApp">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h1 class="login-title">后台管理系统</h1>
                    <p class="login-subtitle">欢迎回来，请登录您的账号</p>
                </div>

                    <form v-on:submit.prevent="login">
                        @Html.AntiForgeryToken()
                        
                    <div class="error-message" v-if="errorMessage" v-text="errorMessage"></div>

                    <div class="form-group">
                        <input type="text" 
                               class="form-control" 
                               v-model="formData.username" 
                               placeholder="请输入用户名" 
                               required 
                               autofocus>
                        <i class="fas fa-user input-icon"></i>
                        </div>

                    <div class="form-group">
                        <input type="password" 
                               class="form-control" 
                               v-model="formData.password" 
                               placeholder="请输入密码" 
                               required>
                        <i class="fas fa-lock input-icon"></i>
                        </div>

                    <div class="remember-row">
                        <div class="custom-checkbox">
                                    <input type="checkbox" id="remember" v-model="formData.rememberMe">
                                    <label for="remember">记住我</label>
                                </div>
                        <a href="#" class="forgot-link">忘记密码？</a>
                            </div>

                    <button type="submit" class="login-btn" :disabled="isLoading">
                        <div class="btn-content">
                            <div v-if="isLoading" class="btn-spinner"></div>
                            <i v-else class="fas fa-sign-in-alt"></i>
                            <span>{{ isLoading ? '登录中...' : '登录' }}</span>
                        </div>
                    </button>
                    </form>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" v-if="isLoading">
            <div class="loading-spinner"></div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <!-- Vue.js 3 -->
    <script src="~/lib/vue/vue.global.js"></script>

    <script>
        // 创建背景粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                // 随机大小和位置
                const size = Math.random() * 4 + 2;
                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 10 + 10;
                const delay = Math.random() * 20;
                
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = startX + 'px';
                particle.style.animationDuration = duration + 's';
                particle.style.animationDelay = delay + 's';
                
                particlesContainer.appendChild(particle);
            }
        }

        // 页面加载完成后创建粒子效果
        document.addEventListener('DOMContentLoaded', createParticles);

        // 配置toastr
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-center",
            timeOut: 3000,
            extendedTimeOut: 1000,
            showEasing: "swing",
            hideEasing: "linear",
            showMethod: "fadeIn",
            hideMethod: "fadeOut"
        };

        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    formData: {
                        username: '',
                        password: '',
                        rememberMe: false
                    },
                    isLoading: false,
                    errorMessage: ''
                };
            },
            methods: {
                // 登录方法
                async login() {
                    this.isLoading = true;
                    this.errorMessage = '';

                    try {
                        const response = await fetch('@Url.Action("Login", "Auth")', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                            },
                            body: JSON.stringify(this.formData)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            toastr.success('登录成功，正在跳转...', '欢迎！');
                            setTimeout(() => {
                            window.location.href = '@Url.Action("Index", "Home")';
                            }, 1000);
                        } else {
                            this.errorMessage = result.message || '登录失败，请检查用户名和密码';
                            toastr.error(this.errorMessage, '登录失败');
                        }
                    } catch (error) {
                        console.error('登录失败:', error);
                        this.errorMessage = '登录失败，请稍后重试';
                        toastr.error('网络连接异常，请稍后重试', '连接失败');
                    } finally {
                        this.isLoading = false;
                    }
                }
            },
            mounted() {
                // 添加键盘事件监听
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !this.isLoading) {
                        this.login();
                    }
                });
            }
        }).mount('#loginApp');
    </script>
</body>
</html> 