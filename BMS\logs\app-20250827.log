[2025-08-27 00:09:28.392 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 00:09:28.427 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 00:09:28.443 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:09:28.445 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:09:28.531 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:29.135 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:29.147 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:09:29.148 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 00:09:30.069 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-27 00:09:45.025 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:09:45.036 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:09:45.042 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.112 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.117 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:09:45.371 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:09:45.373 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:09:45.375 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.422 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.425 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:09:45.461 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:09:45.465 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:09:45.472 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.531 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.537 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:09:45.626 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-27 00:09:45.768 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-27 00:09:45.884 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:09:45.906 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:09:45.911 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.979 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:09:45.982 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:09:46.036 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 00:09:46.085 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 00:09:46.088 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 00:09:46.136 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 00:09:46.161 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 00:09:46.215 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:09:46.223 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:09:46.271 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:09:46.278 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 00:09:46.335 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:09:46.353 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 00:09:46.454 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:09:46.468 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 00:09:46.515 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:09:46.523 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 00:09:46.570 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:09:46.575 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 00:09:46.622 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:10:12.750 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:10:12.753 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:10:12.758 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:10:12.808 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:10:12.810 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:10:12.837 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 00:10:12.887 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:10:12.949 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 0:10:12]
[2025-08-27 00:10:13.028 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:10:13.038 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 )  | 参数: [@task_id0=TASK_123]
[2025-08-27 00:10:13.108 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 ) 
[2025-08-27 00:12:55.414 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:12:55.428 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:12:55.433 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:12:55.708 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:12:55.712 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:12:55.716 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-08-27 00:12:55.762 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:12:55.769 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_001, @task_name=新手指引1, @task_description=完成基础操作指引, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/8/27 0:12:55]
[2025-08-27 00:12:55.884 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:12:55.887 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 )  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:12:55.948 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 ) 
[2025-08-27 00:13:09.999 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:13:10.032 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:13:10.054 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:13:10.099 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:13:10.101 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:13:10.105 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-08-27 00:13:10.147 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:13:10.149 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_001, @task_name=新手指引1, @task_description=完成基础操作指引, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/8/27 0:13:10]
[2025-08-27 00:13:10.279 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:13:10.282 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 )  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:13:10.323 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 ) 
[2025-08-27 00:14:00.479 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:14:00.481 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:14:00.483 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:14:00.534 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:14:00.536 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:14:15.319 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-08-27 00:14:15.363 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:14:15.365 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_001, @task_name=新手指引1, @task_description=完成基础操作指引, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/8/27 0:14:15]
[2025-08-27 00:14:15.447 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:14:15.458 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 )  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:14:15.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 ) 
[2025-08-27 00:14:27.427 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-08-27 00:14:27.476 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:14:27.478 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_001, @task_name=新手指引1, @task_description=完成基础操作指引, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/8/27 0:14:27]
[2025-08-27 00:14:27.571 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:14:27.578 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 )  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:14:27.622 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 ) 
[2025-08-27 00:14:37.136 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_001]
[2025-08-27 00:14:37.182 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:14:53.988 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_001, @task_name=新手指引1, @task_description=完成基础操作指引, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config=[{"Type":"道具","Id":"1001","Amount":10},{"Type":"金币","Id":"gold","Amount":1000}], @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/7/24 0:00:00, @updated_at=2025/8/27 0:14:53]
[2025-08-27 00:14:54.062 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:14:57.813 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 )  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:14:57.857 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM `task_objective` WHERE ( `task_id` = @task_id0 ) 
[2025-08-27 00:22:27.403 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 00:22:27.430 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 00:22:27.435 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:22:27.438 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:22:27.490 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:22:27.944 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:22:27.952 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:22:27.954 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 00:24:46.855 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 00:24:46.927 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 00:24:46.958 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:24:47.013 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:24:47.125 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:47.777 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:47.789 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:24:47.792 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 00:24:49.170 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-27 00:24:53.215 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:24:53.278 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:24:53.283 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:53.349 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:54.669 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:24:54.759 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:24:54.779 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:24:54.890 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.105 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.107 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:24:55.216 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:24:55.227 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:24:55.231 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.310 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.312 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:24:55.339 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:24:55.341 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:24:55.343 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.400 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.404 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:24:55.496 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-27 00:24:55.579 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-27 00:24:55.739 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:24:55.743 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:24:55.747 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.812 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:24:55.814 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:24:56.308 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 00:24:56.405 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 00:24:56.409 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 00:24:56.469 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 00:24:56.506 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 00:24:56.572 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:24:56.577 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:24:56.637 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:24:56.651 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 00:24:56.710 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:24:56.716 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 00:24:56.775 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:24:56.793 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 00:24:56.849 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:24:56.855 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 00:24:56.912 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:24:56.917 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 00:24:56.986 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:01.177 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:25:01.247 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:25:01.257 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:25:01.338 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:25:01.340 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:25:04.614 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 00:25:04.673 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:25:04.720 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 0:25:04]
[2025-08-27 00:25:04.844 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:25:04.851 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 00:25:04.907 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 00:25:04.910 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 00:25:04.965 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:25:04.968 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 00:25:05.024 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:05.035 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:25:05.036 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:25:05.038 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:25:05.095 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:25:05.096 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:25:05.098 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 00:25:05.161 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 00:25:05.165 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 00:25:05.222 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 00:25:05.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 00:25:05.285 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:05.288 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 00:25:05.343 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:05.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 00:25:05.407 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:05.440 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 00:25:05.503 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:05.524 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 00:25:05.595 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:05.974 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 00:25:06.068 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:06.114 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 00:25:06.181 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 00:25:22.079 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 00:25:22.082 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 00:25:22.084 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:25:22.140 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 00:25:22.141 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 00:25:23.735 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 00:25:23.795 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 00:25:23.798 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 0:25:23]
[2025-08-27 00:25:23.904 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 00:25:23.923 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 00:25:23.980 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 10:22:37.442 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 10:22:37.484 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 10:22:37.522 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:37.531 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:37.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:38.281 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:38.299 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:38.303 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 10:22:43.311 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-27 10:22:48.624 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:48.626 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:48.634 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.679 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.680 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:48.789 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:48.789 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:48.790 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:48.791 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:48.792 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:48.793 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:48.793 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:48.794 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.795 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.796 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.797 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:48.803 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.840 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:48.844 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:48.917 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-08-27 10:22:48.965 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-08-27 10:22:49.013 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:49.014 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:49.019 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-08-27 10:22:49.055 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:49.055 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:49.056 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-08-27 10:22:49.057 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:49.058 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:49.201 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/8/27 10:22:49, @MethodConst6=2025/8/27 10:22:49]
[2025-08-27 10:22:49.239 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-27 10:22:49.242 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/8/27 10:22:49, @MethodConst6=2025/8/27 10:22:49, @MethodConst7=, @MethodConst8=, @MethodConst9=1.0, @MethodConst10=1, @MethodConst11=1, @MethodConst12=2025/8/27 10:22:49, @MethodConst13=2025/8/27 10:22:49]
[2025-08-27 10:22:49.328 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10
[2025-08-27 10:22:57.298 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:57.299 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:57.303 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.332 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.333 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:57.394 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:57.394 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:57.394 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:57.394 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:22:57.395 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:57.397 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:57.398 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:57.399 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:22:57.401 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.403 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.405 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.406 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.439 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.440 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:57.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.452 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.453 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:22:57.453 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:57.455 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:57.456 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:22:57.458 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-08-27 10:22:57.459 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))    | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/8/27 10:22:57, @MethodConst6=2025/8/27 10:22:57]
[2025-08-27 10:22:57.459 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-08-27 10:22:57.503 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`item_no` AS CHAR) AS `Value` , `name` AS `Label`  FROM `item_config`  
[2025-08-27 10:22:57.503 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  CAST(`map_id` AS CHAR) AS `Value` , `map_name` AS `Label`  FROM `map_config`  
[2025-08-27 10:22:57.503 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))   
[2025-08-27 10:22:57.508 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10 | 参数: [@MethodConst0=, @MethodConst1=, @MethodConst2=1.0, @MethodConst3=1, @MethodConst4=1, @MethodConst5=2025/8/27 10:22:57, @MethodConst6=2025/8/27 10:22:57, @MethodConst7=, @MethodConst8=, @MethodConst9=1.0, @MethodConst10=1, @MethodConst11=1, @MethodConst12=2025/8/27 10:22:57, @MethodConst13=2025/8/27 10:22:57]
[2025-08-27 10:22:57.555 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `dc`.`id` AS `Id` , `dc`.`map_id` AS `MapId` , IFNULL(`mc`.`map_name`,@MethodConst7) AS `MapName` , `dc`.`drop_type` AS `DropType` , `dc`.`monster_id` AS `MonsterId` , `mm`.`monster_name` AS `MonsterName` , `dc`.`item_id` AS `ItemId` , IFNULL(`ic`.`name`,@MethodConst8) AS `ItemName` , IFNULL(`dc`.`drop_rate`,@MethodConst9) AS `DropRate` , IFNULL(`dc`.`min_count`,@MethodConst10) AS `MinCount` , IFNULL(`dc`.`max_count`,@MethodConst11) AS `MaxCount` , `dc`.`remark` AS `Remark` , IFNULL(`dc`.`create_time`,@MethodConst12) AS `CreateTime` , IFNULL(`dc`.`update_time`,@MethodConst13) AS `UpdateTime` , `dc`.`drop_items_json` AS `DropItemsJson`  FROM `drop_config` `dc` Left JOIN `map_config` `mc` ON ( `dc`.`map_id` = `mc`.`map_id` )  Left JOIN `map_monster` `mm` ON (( `dc`.`monster_id` = `mm`.`monster_id` ) AND ( `dc`.`map_id` = `mm`.`map_id` ))  Left JOIN `item_config` `ic` ON ( `dc`.`item_id` = CAST(`ic`.`item_no` AS CHAR))     ORDER BY `dc`.`create_time` DESC LIMIT 0,10
[2025-08-27 10:23:03.724 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:23:03.743 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:23:03.747 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:03.807 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:03.809 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:23:03.920 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:23:03.922 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:23:03.924 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:03.972 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:03.986 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:23:04.049 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:23:04.127 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:23:04.141 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:04.213 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:04.247 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:23:04.260 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-27 10:23:04.309 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-27 10:23:04.368 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:23:04.378 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:23:04.382 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:04.427 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:23:04.437 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:23:04.477 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 10:23:04.524 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 10:23:04.527 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 10:23:04.572 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 10:23:04.591 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 10:23:04.634 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:23:04.638 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 10:23:04.687 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:23:04.691 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 10:23:04.741 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:23:04.790 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 10:23:04.848 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:23:04.854 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 10:23:04.914 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:23:05.010 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 10:23:05.072 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:23:05.082 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 10:23:05.160 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:24:58.544 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:24:58.547 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:24:58.558 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:24:58.606 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:24:58.609 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:24:58.668 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:24:58.717 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:24:58.774 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 10:24:58]
[2025-08-27 10:24:58.888 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 10:24:58.893 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 10:24:58.936 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 10:25:30.297 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:25:30.299 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:25:30.302 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:25:30.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:25:30.346 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:25:30.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:25:30.391 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:25:30.404 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 10:25:30]
[2025-08-27 10:25:30.549 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 10:25:30.555 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 10:25:30.604 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 10:28:05.265 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:28:05.277 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:28:05.279 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:28:05.542 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:28:05.610 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:28:50.255 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:28:50.350 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:29:51.161 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 10:29:32]
[2025-08-27 10:29:51.304 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 10:30:11.205 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 10:30:11.313 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 10:37:43.810 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 10:37:43.836 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 10:37:43.840 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:37:43.842 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:37:43.890 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:37:44.344 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:37:44.352 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:37:44.353 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 10:38:52.836 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 10:38:52.858 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 10:38:52.862 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:38:52.864 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:38:52.908 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:38:53.330 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:38:53.338 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:38:53.339 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 10:40:57.758 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 10:40:57.866 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 10:40:57.913 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:40:57.979 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:40:58.089 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:40:58.628 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:40:58.638 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:40:58.639 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 10:41:00.027 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-27 10:41:16.614 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:41:16.833 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:41:16.842 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:16.885 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:16.912 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:41:17.059 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:41:17.061 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:41:17.063 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:17.104 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:17.106 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:41:17.139 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:41:17.141 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:41:17.143 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:17.185 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:17.187 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:41:17.220 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-27 10:41:17.270 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-27 10:41:17.307 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:41:17.308 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:41:17.310 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:17.349 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:17.353 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:41:17.387 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 10:41:17.430 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 10:41:17.434 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 10:41:17.475 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 10:41:17.486 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 10:41:17.528 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:17.532 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 10:41:17.574 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:17.576 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 10:41:17.616 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:17.622 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 10:41:17.665 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:17.668 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 10:41:17.712 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:17.716 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 10:41:17.761 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:17.763 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 10:41:17.805 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:35.314 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:41:35.317 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:41:35.319 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:35.379 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:35.384 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:41:41.086 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:41:41.129 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:41:41.168 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 10:41:41]
[2025-08-27 10:41:41.303 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 10:41:41.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 10:41:41.357 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 10:41:41.409 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0) | 参数: [@taskId0=TASK_123, @objectiveType0=REACH_LEVEL, @targetId0=, @targetAmount0=50, @objectiveOrder0=1, @objectiveDescription0=, @createdAt0=2025/8/27 10:41:41]
[2025-08-27 10:41:41.496 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0)
[2025-08-27 10:41:41.504 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:41:41.572 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:41:41.627 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 10:41:41.723 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:46.204 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:41:46.206 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:41:46.208 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:46.250 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:41:46.571 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:41:46.717 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 10:41:46.765 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 10:41:46.768 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 10:41:46.811 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 10:41:46.814 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 10:41:46.855 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:46.858 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 10:41:46.896 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:46.901 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 10:41:46.943 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:46.946 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 10:41:46.987 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:46.990 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 10:41:47.037 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:47.048 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 10:41:47.093 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:41:47.098 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 10:41:47.142 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:42:55.669 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:42:55.670 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:42:55.672 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:42:55.716 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:42:55.717 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:42:55.720 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:42:55.782 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:42:55.785 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 10:42:55.827 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:43:22.584 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:43:22.586 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:43:22.588 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:43:22.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:43:22.637 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:43:22.640 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 10:43:22.682 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:43:22.695 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 10:43:22.735 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 10:43:38.487 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 10:43:38.490 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 10:43:38.499 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:43:38.540 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 10:43:38.542 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 10:43:38.608 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_002]
[2025-08-27 10:43:38.651 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 10:43:38.654 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 10:43:38.693 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:28.929 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 11:04:29.272 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 11:04:29.404 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:04:29.425 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:04:29.516 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:30.119 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:30.129 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:04:30.131 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 11:04:31.152 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-27 11:04:35.868 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:04:35.950 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:04:35.958 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.019 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.083 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:04:36.216 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:04:36.217 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:04:36.219 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.275 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.277 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:04:36.308 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:04:36.310 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:04:36.313 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.365 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.367 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:04:36.408 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-27 11:04:36.474 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-27 11:04:36.530 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:04:36.532 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:04:36.534 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.585 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:04:36.589 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:04:36.626 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:04:36.679 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:04:36.684 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:04:36.736 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:04:36.751 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:04:36.809 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:36.816 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 11:04:36.872 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:36.876 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 11:04:36.929 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:36.934 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 11:04:36.988 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:36.992 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 11:04:37.045 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:37.048 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 11:04:37.098 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:04:37.102 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 11:04:37.161 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:21.264 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:05:21.271 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:05:21.273 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:21.329 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:21.335 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:05:21.353 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:21.402 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:05:21.426 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 11:05:21]
[2025-08-27 11:05:21.530 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 11:05:21.535 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 11:05:21.593 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 11:05:21.638 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1) | 参数: [@taskId0=TASK_123, @objectiveType0=REACH_LEVEL, @targetId0=, @targetAmount0=50, @objectiveOrder0=1, @objectiveDescription0=等级达到50级, @createdAt0=2025/8/27 11:05:21, @taskId1=TASK_123, @objectiveType1=KILL_MONSTER, @targetId1=1, @targetAmount1=10, @objectiveOrder1=1, @objectiveDescription1=击杀怪物, @createdAt1=2025/8/27 11:05:21]
[2025-08-27 11:05:21.707 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0), (@taskId1, @objectiveType1, @targetId1, @targetAmount1, @objectiveOrder1, @objectiveDescription1, @createdAt1)
[2025-08-27 11:05:21.712 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:21.767 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:05:21.770 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:21.836 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:21.846 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:05:21.848 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:05:21.850 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:21.901 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:21.903 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:05:21.905 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:05:21.956 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:05:21.959 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:05:22.011 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:05:22.015 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:22.068 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:22.070 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 11:05:22.126 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:22.130 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 11:05:22.181 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:22.184 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 11:05:22.234 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:22.253 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 11:05:22.304 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:22.307 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 11:05:22.364 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:22.367 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 11:05:22.417 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:26.750 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:05:26.752 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:05:26.753 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:26.802 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:26.803 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:05:26.808 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:26.868 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:05:26.871 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:26.919 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:40.362 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:05:40.369 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:05:40.482 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:40.543 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:40.545 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:05:40.549 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:40.746 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:05:40.750 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id | 参数: [@task_id=TASK_123, @task_name=测试, @task_description=这是一个测试任务, @task_type=0, @is_repeatable=0, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=1, @created_at=2025/8/26 22:51:51, @updated_at=2025/8/27 11:05:40]
[2025-08-27 11:05:40.925 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: UPDATE `task_config`  SET
           `task_name`=@task_name,`task_description`=@task_description,`task_type`=@task_type,`is_repeatable`=@is_repeatable,`prerequisite_task`=@prerequisite_task,`required_pet`=@required_pet,`reward_config`=@reward_config,`is_network_task`=@is_network_task,`is_active`=@is_active,`sort_order`=@sort_order,`created_at`=@created_at,`updated_at`=@updated_at  WHERE `task_id`=@task_id
[2025-08-27 11:05:40.928 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: DELETE FROM task_objective WHERE task_id = @taskId | 参数: [@taskId=TASK_123]
[2025-08-27 11:05:40.989 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: DELETE FROM task_objective WHERE task_id = @taskId
[2025-08-27 11:05:40.991 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0) | 参数: [@taskId0=TASK_123, @objectiveType0=REACH_LEVEL, @targetId0=, @targetAmount0=50, @objectiveOrder0=0, @objectiveDescription0=等级达到50级, @createdAt0=2025/8/27 11:05:40]
[2025-08-27 11:05:41.061 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO task_objective
                           (task_id, objective_type, target_id, target_amount, objective_order, objective_description, created_at)
                           VALUES (@taskId0, @objectiveType0, @targetId0, @targetAmount0, @objectiveOrder0, @objectiveDescription0, @createdAt0)
[2025-08-27 11:05:41.064 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:41.116 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:05:41.118 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:41.167 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:41.176 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:05:41.177 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:05:41.179 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:41.237 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:05:41.239 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:05:41.242 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:05:41.295 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:05:41.296 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:05:41.346 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:05:41.349 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:05:41.398 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:41.400 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 11:05:41.459 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:41.462 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 11:05:41.511 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:41.514 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 11:05:41.567 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:41.575 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 11:05:41.627 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:41.631 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 11:05:41.688 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:05:42.072 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 11:05:42.177 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:14:50.581 +08:00 INF] BMS: 🚀 应用程序启动，日志系统已配置
[2025-08-27 11:14:50.610 +08:00 DBG] BMS: 🔧 调试级别日志测试
[2025-08-27 11:14:50.614 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:14:50.616 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:14:50.665 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:14:51.155 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:14:51.162 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:14:51.163 +08:00 INF] BMS: ✅ DbContext 初始化成功，日志文件位置: logs/
[2025-08-27 11:15:49.377 +08:00 WRN] Microsoft.AspNetCore.HttpsPolicy.HttpsRedirectionMiddleware: Failed to determine the https port for redirect.
[2025-08-27 11:15:49.411 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:15:49.412 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:15:49.415 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:15:49.468 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:15:49.469 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:16:05.420 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:16:05.421 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:16:05.422 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:05.507 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:05.509 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:16:12.905 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:16:12.906 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:16:12.908 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:12.957 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:12.958 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:16:12.974 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:16:12.974 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:16:12.976 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:13.031 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:13.032 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:16:13.058 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC  | 参数: [@is_active0=1]
[2025-08-27 11:16:13.114 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT  `task_id` AS `Value` , `task_name` AS `Label`  FROM `task_config`  WHERE ( `is_active` = @is_active0 )ORDER BY `sort_order` ASC 
[2025-08-27 11:16:13.145 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:16:13.146 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:16:13.147 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:13.201 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:16:13.202 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:16:13.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:16:13.275 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:16:13.277 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:16:13.329 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:16:13.338 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:16:13.393 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:16:13.395 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 11:16:13.445 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:16:13.446 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 11:16:13.495 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:16:13.497 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 11:16:13.549 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:16:13.551 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 11:16:13.607 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:16:13.608 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 11:16:13.658 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:16:13.659 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 11:16:13.708 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.096 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:17:04.097 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:17:04.098 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:04.146 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:04.147 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:17:04.162 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_20250827_111704_229]
[2025-08-27 11:17:04.211 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:17:04.227 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: INSERT INTO `task_config`  
           (`task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at`)
     VALUES
           (@task_id,@task_name,@task_description,@task_type,@is_repeatable,@prerequisite_task,@required_pet,@reward_config,@is_network_task,@is_active,@sort_order,@created_at,@updated_at) ; | 参数: [@task_id=TASK_20250827_111704_229, @task_name=测试2, @task_description=123, @task_type=0, @is_repeatable=1, @prerequisite_task=, @required_pet=, @reward_config={"exp": 100, "gold": 50}, @is_network_task=0, @is_active=1, @sort_order=2, @created_at=2025/8/27 11:17:04, @updated_at=2025/8/27 11:17:04]
[2025-08-27 11:17:04.361 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: INSERT INTO `task_config`  
           (`task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at`)
     VALUES
           (@task_id,@task_name,@task_description,@task_type,@is_repeatable,@prerequisite_task,@required_pet,@reward_config,@is_network_task,@is_active,@sort_order,@created_at,@updated_at) ;
[2025-08-27 11:17:04.364 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1 | 参数: [@task_id0=TASK_20250827_111704_229]
[2025-08-27 11:17:04.411 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`   WHERE ( `task_id` = @task_id0 )   LIMIT 0,1
[2025-08-27 11:17:04.413 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_20250827_111704_229]
[2025-08-27 11:17:04.460 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.465 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:17:04.466 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:17:04.467 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:04.525 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:04.527 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:17:04.529 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:17:04.578 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT Count(*) FROM `task_config`  
[2025-08-27 11:17:04.579 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:17:04.640 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `task_id`,`task_name`,`task_description`,`task_type`,`is_repeatable`,`prerequisite_task`,`required_pet`,`reward_config`,`is_network_task`,`is_active`,`sort_order`,`created_at`,`updated_at` FROM `task_config`    ORDER BY `sort_order` ASC,`created_at` DESC LIMIT 0,10
[2025-08-27 11:17:04.642 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_123]
[2025-08-27 11:17:04.698 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.700 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_001]
[2025-08-27 11:17:04.767 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.768 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_20250827_111704_229]
[2025-08-27 11:17:04.821 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.822 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_002]
[2025-08-27 11:17:04.872 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.873 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_003]
[2025-08-27 11:17:04.921 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.923 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_004]
[2025-08-27 11:17:04.973 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:04.974 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_005]
[2025-08-27 11:17:05.024 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:05.026 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC  | 参数: [@task_id0=TASK_010]
[2025-08-27 11:17:05.074 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT `objective_id`,`task_id`,`objective_type`,`target_id`,`target_amount`,`objective_order`,`objective_description`,`created_at` FROM `task_objective`  WHERE ( `task_id` = @task_id0 )ORDER BY `objective_order` ASC 
[2025-08-27 11:17:23.680 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:17:23.681 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:17:23.682 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:23.733 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:23.734 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
[2025-08-27 11:17:51.110 +08:00 INF] BMS.Sugar.DbContext: 初始化数据库连接: Server=**************;Database=kddata;User=app_user;Password=***;CharSet=utf8mb4;AllowUserVariables=true;Port=3306;
[2025-08-27 11:17:51.111 +08:00 INF] BMS.Sugar.DbContext: 开始测试数据库连接...
[2025-08-27 11:17:51.112 +08:00 DBG] BMS.Sugar.DbContext: 执行SQL: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:51.163 +08:00 DBG] BMS.Sugar.DbContext: SQL执行完成: SELECT * FROM (SELECT 1 as test) t     LIMIT 0,1
[2025-08-27 11:17:51.164 +08:00 INF] BMS.Sugar.DbContext: ✅ 数据库连接测试成功
