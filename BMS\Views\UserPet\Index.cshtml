@{
    ViewData["Title"] = "用户宠物管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --card-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        --card-hover-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        --border-radius: 16px;
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modern-page {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .modern-card:hover {
        box-shadow: var(--card-hover-shadow);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 1.5rem;
    }

    .modern-card-title {
        font-weight: 600;
        font-size: 1.25rem;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1em;
    }

    .modern-btn {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-secondary {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: #f8f9fa;
    }

    .modern-btn-success {
        background: var(--success-gradient);
        color: #f8f9fa;
    }

    .modern-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-warning {
        background: var(--warning-gradient);
        color: #f8f9fa;
    }

    .modern-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-info {
        background: var(--info-gradient);
        color: #f8f9fa;
    }

    .modern-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-danger {
        background: var(--danger-gradient);
        color: #f8f9fa;
    }

    .modern-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
        color: #f8f9fa;
    }

    .modern-form-control {
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
    }

    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        background: white;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: none;
        font-weight: 600;
        color: #475569;
        padding: 1rem;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.001);
    }

    .modern-table tbody td {
        padding: 1rem;
        border-color: #f1f5f9;
    }

    .modern-pagination {
        gap: 0.5rem;
    }

    .modern-pagination .page-link {
        border-radius: 10px;
        border: none;
        margin: 0 2px;
        padding: 0.6rem 1rem;
        transition: var(--transition);
    }

    .modern-pagination .page-item.active .page-link {
        background: var(--primary-gradient);
        border: none;
    }

    .modern-pagination .page-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .modern-badge {
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        font-weight: 500;
        font-size: 0.75rem;
    }

    .modern-breadcrumb {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 0;
    }

    .nav-tabs .nav-link.active {
        background: var(--primary-gradient) !important;
        color: #f8f9fa !important;
        border: none !important;
        border-radius: 12px 12px 0 0 !important;
    }

    .nav-tabs .nav-link {
        border-radius: 12px 12px 0 0;
        border: none;
        margin-right: 0.25rem;
        transition: var(--transition);
    }

    .nav-tabs .nav-link:hover {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }

    .fade-in-delay-1 {
        animation: fadeInUp 0.6s ease 0.1s forwards;
        opacity: 0;
    }

    .fade-in-delay-2 {
        animation: fadeInUp 0.6s ease 0.2s forwards;
        opacity: 0;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-header h1 {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }
</style>

<div class="modern-page">

<!-- 主要内容 -->
<section class="content">
    <div class="container-fluid">
        <div id="userPetApp">
            <!-- 页面标题 -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0 fade-in">
                                <i class="fas fa-paw mr-2"></i>用户宠物管理
                            </h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="modern-breadcrumb breadcrumb float-sm-right fade-in-delay-1">
                                <li class="breadcrumb-item"><a href="/Home" style="color: #667eea; text-decoration: none;">首页</a></li>
                                <li class="breadcrumb-item active">用户宠物管理</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 选项卡导航 -->
            <div class="card card-primary card-tabs">
                <div class="card-header p-0 pt-1">
                    <ul class="nav nav-tabs" id="custom-tabs-two-tab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="user-pet-tab" data-bs-toggle="tab" href="#user-pet-content" role="tab" aria-controls="user-pet-content" aria-selected="true">
                                <i class="fas fa-dragon mr-1"></i>用户宠物管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="realm-tab" data-bs-toggle="tab" href="#realm-content" role="tab" aria-controls="realm-content" aria-selected="false">
                                <i class="fas fa-mountain mr-1"></i>境界管理
                            </a>
                        </li>

                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="custom-tabs-two-tabContent">
                        <!-- 用户宠物管理选项卡 -->
                        <div class="tab-pane fade show active" id="user-pet-content" role="tabpanel" aria-labelledby="user-pet-tab">

            <!-- 搜索条件 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">搜索条件</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>用户ID</label>
                                <input type="number" v-model.number="queryForm.userId" class="form-control" placeholder="请输入用户ID">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>用户名</label>
                                <input type="text" v-model="queryForm.userName" class="form-control" placeholder="请输入用户名">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>宠物序号</label>
                                <input type="number" v-model.number="queryForm.petNo" class="form-control" placeholder="请输入宠物序号">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>宠物名称</label>
                                <input type="text" v-model="queryForm.petName" class="form-control" placeholder="请输入宠物名称">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>宠物属性</label>
                                <select v-model="queryForm.petAttribute" class="form-control">
                                    <option value="">全部</option>
                                    <option value="火">火</option>
                                    <option value="水">水</option>
                                    <option value="木">木</option>
                                    <option value="金">金</option>
                                    <option value="土">土</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>境界</label>
                                <select v-model="queryForm.realm" class="form-control">
                                    <option value="">全部境界</option>
                                    <option v-for="realm in realmOptions" v-bind:key="realm.value || realm.realmId" v-bind:value="realm.value || realm.realmName">
                                        {{ realm.label || realm.realmName }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>元素</label>
                                <input type="text" v-model="queryForm.element" class="form-control" placeholder="请输入元素">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>宠物状态</label>
                                <select v-model="queryForm.status" class="form-control">
                                    <option value="">全部状态</option>
                                    <option value="牧场">牧场</option>
                                    <option value="携带">携带</option>
                                    <option value="丢弃">丢弃</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>是否主战</label>
                                <select v-model="queryForm.isMain" class="form-control">
                                    <option value="">全部</option>
                                    <option v-bind:value="true">主战宠物</option>
                                    <option v-bind:value="false">非主战宠物</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="d-block">
                                    <button type="button" class="btn btn-primary" v-on:click="searchUserPets" v-bind:disabled="loading">
                                        <i class="fas fa-search"></i> 搜索
                                    </button>
                                    <button type="button" class="btn btn-secondary ml-2" v-on:click="resetQuery">
                                        <i class="fas fa-redo"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-success ml-2" v-on:click="showAddModal">
                                        <i class="fas fa-plus"></i> 新增宠物
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据列表 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">用户宠物列表</h3>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>宠物编号</th>
                                <th>形象编号</th>
                                <th>宠物</th>
                                <th>属性</th>
                                <th>等级</th>
                                <th>生命值</th>
                                <th>魔法值</th>
                                <th>攻击力</th>
                                <th>防御力</th>
                                <th>速度</th>
                                <th>境界</th>
                                <th>进化次数</th>
                                <th>状态</th>
                                <th>主战</th>
                                <th>技能数量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="loading">
                                <td colspan="18" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                            <tr v-else-if="userPetsList.length === 0">
                                <td colspan="18" class="text-center">暂无数据</td>
                            </tr>
                            <tr v-else v-for="userPet in userPetsList" v-bind:key="userPet.id">
                                <td>{{ userPet.id }}</td>
                                <td>{{ userPet.userName }}</td>
                                <td>{{ userPet.petNo }}</td>
                                <td>{{ userPet.image || '-' }}</td>
                                <td>{{ userPet.petName }}</td>
                                <td>{{ userPet.petAttribute }}</td>
                                <td>{{ calculateLevel(userPet.exp) }}</td>
                                <td>{{ userPet.hp }}</td>
                                <td>{{ userPet.mp }}</td>
                                <td>{{ userPet.atk }}</td>
                                <td>{{ userPet.def }}</td>
                                <td>{{ userPet.spd }}</td>
                                <td>{{ userPet.realm || '无' }}</td>
                                <td>{{ userPet.evolveCount || 0 }}</td>
                                <td>
                                    <span v-bind:class="{
                                        'badge-success': userPet.status === '携带',
                                        'badge-secondary': userPet.status === '牧场',
                                        'badge-danger': userPet.status === '丢弃'
                                    }">
                                        {{ userPet.status || '牧场' }}
                                    </span>
                                </td>
                                <td>
                                    <span v-if="userPet.isMain" class="badge-warning">
                                        <i class="fas fa-star"></i> 主战
                                    </span>
                                    <span v-else class="text-muted">-</span>
                                </td>
                                <td>{{ userPet.skills ? userPet.skills.length : 0 }}</td>
                                <td>
                                    <button type="button" class="btn btn-info btn-sm" v-on:click="viewDetail(userPet)">
                                        <i class="fas fa-eye"></i> 查看
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm ml-1" v-on:click="editUserPet(userPet)">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                    <button type="button" class="btn btn-primary btn-sm ml-1" v-on:click="manageSkills(userPet)">
                                        <i class="fas fa-magic"></i> 技能
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm ml-1" v-on:click="evolvePet(userPet)">
                                        <i class="fas fa-arrow-up"></i> 进化
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm ml-1" v-on:click="deleteUserPet(userPet)">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer" v-if="totalCount > 0" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 0 0 var(--border-radius) var(--border-radius); border-top: 1px solid rgba(0, 0, 0, 0.05); padding: 1.5rem;">
                    <div class="row align-items-center">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info" style="color: #64748b; font-weight: 500;">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers float-right">
                                <ul class="modern-pagination pagination">
                                    <li class="paginate_button page-item previous" v-bind:class="{ disabled: currentPage === 1 }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="paginate_button page-item" 
                                        v-for="page in visiblePages" 
                                        v-bind:key="page" 
                                        v-bind:class="{ active: page === currentPage }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="paginate_button page-item next" v-bind:class="{ disabled: currentPage === totalPages }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增/编辑用户宠物模态框 -->
<div class="modal fade" id="userPetModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{ isEdit ? '编辑用户宠物' : '新增用户宠物' }}</h4>
                <button type="button" class="close" v-on:click="closeModal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row" v-if="!isEdit">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>用户 <span class="text-danger">*</span></label>
                                <select v-model="userPetForm.userId" class="form-control" required>
                                    <option value="">请选择用户</option>
                                    <option v-for="user in userOptions" v-bind:key="user.value" v-bind:value="user.value">
                                        {{ user.label }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>宠物配置 <span class="text-danger">*</span></label>
                                <select v-model="userPetForm.petNo" class="form-control" required>
                                    <option value="">请选择宠物</option>
                                    <option v-for="pet in petConfigOptions" v-bind:key="pet.petNo" v-bind:value="pet.petNo">
                                        {{ pet.name }} ({{ pet.attribute }})
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>头像编号</label>
                                <input type="number" v-model.number="userPetForm.image" class="form-control" placeholder="请输入头像编号">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>经验值</label>
                                <input type="number" v-model.number="userPetForm.exp" class="form-control" placeholder="请输入经验值">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>生命值</label>
                                <input type="number" v-model.number="userPetForm.hp" class="form-control" placeholder="请输入生命值">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>魔法值</label>
                                <input type="number" v-model.number="userPetForm.mp" class="form-control" placeholder="请输入魔法值">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>攻击力</label>
                                <input type="number" v-model.number="userPetForm.atk" class="form-control" placeholder="请输入攻击力">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>防御力</label>
                                <input type="number" v-model.number="userPetForm.def" class="form-control" placeholder="请输入防御力">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>速度</label>
                                <input type="number" v-model.number="userPetForm.spd" class="form-control" placeholder="请输入速度">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>闪避</label>
                                <input type="number" v-model.number="userPetForm.dodge" class="form-control" placeholder="请输入闪避">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>成长值</label>
                                <input type="number" step="0.01" v-model.number="userPetForm.growth" class="form-control" placeholder="请输入成长值">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>命中</label>
                                <input type="number" v-model.number="userPetForm.hit" class="form-control" placeholder="请输入命中">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>状态</label>
                                <input type="number" v-model.number="userPetForm.state" class="form-control" placeholder="请输入状态编号">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>境界</label>
                                <select v-model="userPetForm.realm" class="form-control">
                                    <option value="">请选择境界</option>
                                    <option v-for="realm in realmOptions" v-bind:key="realm.value || realm.realmId" v-bind:value="realm.value || realm.realmName">
                                        {{ realm.label || realm.realmName }}
                                    </option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>元素</label>
                                <input type="text" v-model="userPetForm.element" class="form-control" placeholder="请输入元素">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>进化次数</label>
                                <input type="number" v-model.number="userPetForm.evolveCount" class="form-control" placeholder="请输入进化次数">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>宠物状态 <span class="text-danger">*</span></label>
                                <select v-model="userPetForm.status" class="form-control" required>
                                    <option value="">请选择状态</option>
                                    <option value="牧场">牧场</option>
                                    <option value="携带">携带</option>
                                    <option value="丢弃">丢弃</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="form-check d-block">
                                    <input type="checkbox" v-model="userPetForm.isMain" class="form-check-input" id="isMainCheck">
                                    <label class="form-check-label" for="isMainCheck">
                                        设置为主战宠物
                                    </label>
                                    <small class="form-text text-muted">每个用户只能有一个主战宠物</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info mb-0">
                                <h6><i class="fas fa-info-circle"></i> 宠物携带规则说明：</h6>
                                <ul class="mb-0">
                                    <li>每个用户最多可拥有 <strong>3只</strong> 携带状态的宠物（包括主战宠物）</li>
                                    <li>每个用户只能有 <strong>1只</strong> 主战宠物</li>
                                    <li>正常搭配：2只携带 + 1只主战，也可以3只都设为携带</li>
                                    <li>设置新的主战宠物时，原主战宠物将自动改为携带状态</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" v-on:click="closeModal">取消</button>
                <button type="button" class="btn btn-primary" v-on:click="saveUserPet" v-bind:disabled="saving">
                    <i v-if="saving" class="fas fa-spinner fa-spin"></i>
                    {{ saving ? '保存中...' : '保存' }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 宠物详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">宠物详情</h4>
                <button type="button" class="close" v-on:click="closeDetailModal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" v-if="selectedUserPet">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr><td>用户名</td><td>{{ selectedUserPet.userName }}</td></tr>
                            <tr><td>宠物编号</td><td>{{ selectedUserPet.petNo }}</td></tr>
                            <tr><td>形象编号</td><td>{{ selectedUserPet.image || '-' }}</td></tr>
                            <tr><td>宠物名称</td><td>{{ selectedUserPet.petName }}</td></tr>
                            <tr><td>宠物属性</td><td>{{ selectedUserPet.petAttribute }}</td></tr>
                            <tr><td>等级</td><td>{{ calculateLevel(selectedUserPet.exp) }}</td></tr>
                            <tr><td>经验值</td><td>{{ selectedUserPet.exp }}</td></tr>
                            <tr><td>生命值</td><td>{{ selectedUserPet.hp }}</td></tr>
                            <tr><td>魔法值</td><td>{{ selectedUserPet.mp }}</td></tr>
                            <tr><td>宠物状态</td><td>
                                <span v-bind:class="{
                                    'badge-success': selectedUserPet.status === '携带',
                                    'badge-secondary': selectedUserPet.status === '牧场',
                                    'badge-danger': selectedUserPet.status === '丢弃'
                                }">
                                    {{ selectedUserPet.status || '牧场' }}
                                </span>
                            </td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr><td>攻击力</td><td>{{ selectedUserPet.atk }}</td></tr>
                            <tr><td>防御力</td><td>{{ selectedUserPet.def }}</td></tr>
                            <tr><td>速度</td><td>{{ selectedUserPet.spd }}</td></tr>
                            <tr><td>闪避</td><td>{{ selectedUserPet.dodge }}</td></tr>
                            <tr><td>命中</td><td>{{ selectedUserPet.hit }}</td></tr>
                            <tr><td>成长值</td><td>{{ selectedUserPet.growth }}</td></tr>
                            <tr><td>进化次数</td><td>{{ selectedUserPet.evolveCount || 0 }}</td></tr>
                            <tr><td>是否主战</td><td>
                                <span v-if="selectedUserPet.isMain" class="badge-warning">
                                    <i class="fas fa-star"></i> 主战宠物
                                </span>
                                <span v-else class="text-muted">非主战宠物</span>
                            </td></tr>
                        </table>
                    </div>
                </div>
                <div class="row" v-if="selectedUserPet.skills && selectedUserPet.skills.length > 0">
                    <div class="col-md-12">
                        <h5>技能列表</h5>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>技能名称</th>
                                    <th>技能等级</th>
                                    <th>学习时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="skill in selectedUserPet.skills" v-bind:key="skill.id">
                                    <td>{{ skill.skillName }}</td>
                                    <td>{{ skill.skillLevel }}</td>
                                    <td>{{ formatDateTime(skill.createTime) }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" v-on:click="closeDetailModal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 技能管理模态框 -->
<div class="modal fade" id="skillModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">技能管理 - {{ skillManagePet ? skillManagePet.petName : '' }}</h4>
                <button type="button" class="close" v-on:click="closeSkillModal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-12">
                        <button type="button" class="btn btn-success" v-on:click="showAddSkillForm">
                            <i class="fas fa-plus"></i> 添加技能
                        </button>
                        <button type="button" class="btn btn-info ml-2" v-on:click="assignSkillsByConfig">
                            <i class="fas fa-magic"></i> 自动分配技能
                        </button>
                    </div>
                </div>
                
                <div v-if="showSkillForm" class="row mb-3">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>添加技能</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>技能ID <span class="text-danger">*</span></label>
                                            <input type="number" v-model.number="skillForm.skillId" class="form-control" placeholder="请输入技能ID">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>技能等级 <span class="text-danger">*</span></label>
                                            <input type="number" v-model.number="skillForm.skillLevel" class="form-control" placeholder="请输入技能等级" min="1" max="10">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <button type="button" class="btn btn-primary" v-on:click="addSkill" v-bind:disabled="skillSaving">
                                            <i v-if="skillSaving" class="fas fa-spinner fa-spin"></i>
                                            {{ skillSaving ? '添加中...' : '添加技能' }}
                                        </button>
                                        <button type="button" class="btn btn-secondary ml-2" v-on:click="cancelAddSkill">取消</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>技能ID</th>
                                <th>技能名称</th>
                                <th>技能等级</th>
                                <th>学习时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="skillLoading">
                                <td colspan="5" class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                                </td>
                            </tr>
                            <tr v-else-if="petSkills.length === 0">
                                <td colspan="5" class="text-center">暂无技能</td>
                            </tr>
                            <tr v-else v-for="skill in petSkills" v-bind:key="skill.id">
                                <td>{{ skill.skillId }}</td>
                                <td>{{ skill.skillName }}</td>
                                <td>{{ skill.skillLevel }}</td>
                                <td>{{ formatDateTime(skill.createTime) }}</td>
                                <td>
                                    <button type="button" class="btn btn-warning btn-sm" v-on:click="upgradeSkill(skill)">
                                        <i class="fas fa-arrow-up"></i> 升级
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm ml-1" v-on:click="deleteSkill(skill)">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" v-on:click="closeSkillModal">关闭</button>
            </div>
        </div>
    </div>
</div>
                        </div>
                        
                        <!-- 境界管理选项卡 -->
                        <div class="tab-pane fade" id="realm-content" role="tabpanel" aria-labelledby="realm-tab">
                            <!-- 境界搜索表单 -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">境界搜索</h3>
                                    <div class="card-tools">
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-lg-3 col-md-4 col-sm-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-hashtag text-primary mr-1"></i>境界ID
                                                </label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" placeholder="输入境界ID" v-model.number="realmQueryForm.realmId">
                                                    <div class="input-group-append" v-if="realmQueryForm.realmId">
                                                        <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="realmQueryForm.realmId = null" title="清除">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-4 col-sm-6">
                                            <div class="form-group">
                                                <label class="form-label">
                                                    <i class="fas fa-mountain text-success mr-1"></i>境界名称
                                                </label>
                                                <div class="input-group">
                                                    <input type="text" class="form-control" placeholder="输入境界名称" v-model="realmQueryForm.realmName">
                                                    <div class="input-group-append" v-if="realmQueryForm.realmName">
                                                        <button class="btn btn-outline-secondary btn-sm" type="button" v-on:click="realmQueryForm.realmName = ''" title="清除">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-4 col-sm-12">
                                            <div class="form-group">
                                                <label class="form-label text-transparent">操作</label>
                                                <div class="btn-group d-flex" role="group">
                                                    <button type="button" class="btn btn-primary" v-on:click="searchRealms" title="搜索境界">
                                                        <i class="fas fa-search"></i> 搜索
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary" v-on:click="resetRealmSearch" title="重置搜索条件">
                                                        <i class="fas fa-undo"></i> 重置
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-12 col-sm-12">
                                            <div class="form-group">
                                                <label class="form-label text-transparent">新增</label>
                                                <div>
                                                    <button type="button" class="btn btn-success" v-on:click="showAddRealmModal" title="新增境界">
                                                        <i class="fas fa-plus mr-1"></i>新增境界
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 统计信息 -->
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="text-muted">
                                                <i class="fas fa-info-circle mr-1"></i>
                                                <span v-if="realmTotalCount > 0">找到 {{ realmTotalCount }} 条境界记录</span>
                                                <span v-else>暂无境界数据</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 境界列表 -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">境界列表</h3>
                                </div>
                                <div class="card-body">
                                    <div v-if="realmLoading" class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="sr-only">加载中...</span>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 100px;">境界ID</th>
                                                        <th>境界名称</th>
                                                        <th style="width: 100px;">关联宠物数</th>
                                                        <th style="width: 150px;">操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr v-for="realm in realmsList" v-bind:key="realm.realmId">
                                                        <td>{{ realm.realmId }}</td>
                                                        <td>{{ realm.realmName }}</td>
                                                        <td class="text-center">
                                                            <span class="badge-info">{{ realm.petCount || 0 }}</span>
                                                        </td>
                                                        <td>
                                                            <div class="btn-group btn-group-sm" role="group">
                                                                <button type="button" class="btn btn-outline-warning" v-on:click="showEditRealmModal(realm)" title="编辑境界">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button type="button" class="btn btn-outline-danger" v-on:click="deleteRealm(realm)" title="删除境界" v-bind:disabled="realm.petCount > 0">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr v-if="realmsList.length === 0">
                                                        <td colspan="4" class="text-center">暂无数据</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- 分页 -->
                                        <div class="row mt-3" v-if="realmTotalCount > 0" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: var(--border-radius); padding: 1.5rem;">
                                            <div class="col-sm-5">
                                                <div class="dataTables_info" style="color: #64748b; font-weight: 500;">
                                                    显示第 {{ (realmCurrentPage - 1) * realmPageSize + 1 }} 到 {{ Math.min(realmCurrentPage * realmPageSize, realmTotalCount) }} 条记录，共 {{ realmTotalCount }} 条
                                                </div>
                                            </div>
                                            <div class="col-sm-7">
                                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                                    <ul class="modern-pagination pagination">
                                                        <li class="paginate_button page-item" v-bind:class="{ disabled: realmCurrentPage === 1 }">
                                                            <a href="#" class="page-link" v-on:click.prevent="changeRealmPage(realmCurrentPage - 1)">上一页</a>
                                                        </li>
                                                        <li class="paginate_button page-item" v-for="page in realmVisiblePages" v-bind:key="page" v-bind:class="{ active: page === realmCurrentPage }">
                                                            <a href="#" class="page-link" v-on:click.prevent="changeRealmPage(page)">{{ page }}</a>
                                                        </li>
                                                        <li class="paginate_button page-item" v-bind:class="{ disabled: realmCurrentPage === realmTotalPages }">
                                                            <a href="#" class="page-link" v-on:click.prevent="changeRealmPage(realmCurrentPage + 1)">下一页</a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        

                    </div>
                </div>
            </div>

            <!-- 新增/编辑境界模态框 -->
            <div class="modal fade" id="realmModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">{{ isRealmEdit ? '编辑境界' : '新增境界' }}</h4>
                            <button type="button" class="close" v-on:click="closeRealmModal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form>
                                <div class="form-group">
                                    <label>境界ID <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" v-model.number="realmForm.realmId" v-bind:disabled="isRealmEdit" placeholder="请输入境界ID（如：1, 2, 3等）">
                                    <small class="form-text text-muted">境界ID用于程序识别，创建后不可修改，建议按顺序递增</small>
                                </div>
                                <div class="form-group">
                                    <label>境界名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" v-model="realmForm.realmName" placeholder="请输入境界名称（如：筑基期、金丹期等）">
                                    <small class="form-text text-muted">境界名称用于前端显示</small>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-outline-secondary" v-on:click="closeRealmModal">
                                <i class="fas fa-times mr-1"></i>取消
                            </button>
                            <button type="button" class="btn btn-primary" v-on:click="saveRealm" v-bind:disabled="realmSaving">
                                <span v-if="realmSaving" class="spinner-border spinner-border-sm mr-1"></span>
                                <i v-else class="fas fa-save mr-1"></i>
                                {{ isRealmEdit ? '更新境界' : '创建境界' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
    </div>



<script>
    const { createApp, ref, computed, onMounted } = Vue;

    createApp({
        setup() {
            // 响应式数据
            const loading = ref(false);
            const saving = ref(false);
            const skillLoading = ref(false);
            const skillSaving = ref(false);
            const userPetsList = ref([]);
            const totalCount = ref(0);
            const currentPage = ref(1);
            const pageSize = ref(10);
            const isEdit = ref(false);
            const selectedUserPet = ref(null);
            const skillManagePet = ref(null);
            const petSkills = ref([]);
            const showSkillForm = ref(false);
            
            // 下拉选项
            const userOptions = ref([]);
            const petConfigOptions = ref([]);
            const realmOptions = ref([]);
            const statusOptions = ref([]);

            // 查询表单
            const queryForm = ref({
                userId: null,
                userName: '',
                petNo: null,
                petName: '',
                petAttribute: '',
                realm: '',
                element: '',
                status: '',
                isMain: null,
                page: 1,
                pageSize: 10
            });

            // 用户宠物表单
            const userPetForm = ref({
                id: 0,
                userId: null,
                petNo: null,
                image: null,
                exp: 0,
                hp: 100,
                mp: 100,
                atk: 10,
                def: 10,
                spd: 10,
                state: null,
                dodge: 10,
                growth: 1.0,
                hit: 10,
                realm: '',
                evolveCount: 0,
                element: '',
                status: '牧场',
                isMain: false
            });

            // 技能表单
            const skillForm = ref({
                userPetId: 0,
                skillId: null,
                skillLevel: 1
            });

            // ==================== 境界管理数据 ====================
            const realmLoading = ref(false);
            const realmSaving = ref(false);
            const realmsList = ref([]);
            const realmTotalCount = ref(0);
            const realmCurrentPage = ref(1);
            const realmPageSize = ref(10);
            
            const realmQueryForm = ref({
                realmId: null,
                realmName: '',
                page: 1,
                pageSize: 10
            });
            
            const realmForm = ref({
                realmId: null,
                realmName: ''
            });
            
            const isRealmEdit = ref(false);



            // 计算属性
            const totalPages = computed(() => {
                return Math.ceil(totalCount.value / pageSize.value);
            });

            const visiblePages = computed(() => {
                const current = currentPage.value;
                const total = totalPages.value;
                const pages = [];
                
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            });

            // 境界管理计算属性
            const realmTotalPages = computed(() => {
                return Math.ceil(realmTotalCount.value / realmPageSize.value);
            });

            const realmVisiblePages = computed(() => {
                const current = realmCurrentPage.value;
                const total = realmTotalPages.value;
                const pages = [];
                
                let start = Math.max(1, current - 2);
                let end = Math.min(total, current + 2);
                
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                
                return pages;
            });



            // 方法
            const loadUserPets = async () => {
                try {
                    loading.value = true;
                    const requestData = {
                        ...queryForm.value,
                        page: currentPage.value,
                        pageSize: pageSize.value
                    };
                    
                    const response = await axios.post('/UserPet/GetList', requestData);
                    if (response.data.code === 200) {
                        userPetsList.value = response.data.data || [];
                        totalCount.value = response.data.total || 0;
                    } else {
                        console.error('获取用户宠物列表失败：', response.data.message);
                    }
                } catch (error) {
                    console.error('获取用户宠物列表失败：', error);
                } finally {
                    loading.value = false;
                }
            };

            const loadRealmOptions = async () => {
                try {
                    console.log('开始加载境界选项...');
                    const response = await axios.get('/UserPet/GetRealmOptions');
                    console.log('境界选项API完整响应：', response.data);
                    
                    // 检查ApiResult格式
                    if (response.data.success && response.data.data) {
                        realmOptions.value = response.data.data;
                        console.log('境界选项已设置（ApiResult格式）：', realmOptions.value);
                        console.log('第一个境界选项结构：', realmOptions.value[0]);
                    } else if (response.data.code === 200 && response.data.data) {
                        realmOptions.value = response.data.data;
                        console.log('境界选项已设置（自定义格式）：', realmOptions.value);
                        console.log('第一个境界选项结构：', realmOptions.value[0]);
                    } else {
                        console.warn('境界选项API返回数据格式异常，响应：', response.data);
                        realmOptions.value = [];
                    }
                } catch (error) {
                    console.error('获取境界选项失败：', error);
                    realmOptions.value = [];
                }
            };

            const loadStatusOptions = async () => {
                try {
                    const response = await axios.get('/UserPet/GetStatusOptions');
                    if (response.data.code === 200) {
                        statusOptions.value = response.data.data || [];
                    }
                } catch (error) {
                    console.error('获取状态选项失败：', error);
                }
            };

            const searchUserPets = () => {
                currentPage.value = 1;
                loadUserPets();
            };

            const resetQuery = () => {
                queryForm.value = {
                    userId: null,
                    userName: '',
                    petNo: null,
                    petName: '',
                    petAttribute: '',
                    realm: '',
                    element: '',
                    status: '',
                    isMain: null,
                    page: 1,
                    pageSize: 10
                };
                searchUserPets();
            };

            const resetForm = () => {
                userPetForm.value = {
                    id: 0,
                    userId: null,
                    petNo: null,
                    image: null,
                    exp: 0,
                    hp: 100,
                    mp: 100,
                    atk: 10,
                    def: 10,
                    spd: 10,
                    state: null,
                    dodge: 10,
                    growth: 1.0,
                    hit: 10,
                    realm: '',
                    evolveCount: 0,
                    element: '',
                    status: '牧场',
                    isMain: false
                };
            };

            const changePage = (page) => {
                if (page >= 1 && page <= totalPages.value) {
                    currentPage.value = page;
                    loadUserPets();
                }
            };

            const loadOptions = async () => {
                try {
                    // 加载用户选项
                    const userResponse = await axios.get('/UserPet/GetUserOptions');
                    if (userResponse.data.code === 200) {
                        userOptions.value = userResponse.data.data;
                    }

                    // 加载宠物配置选项
                    const petResponse = await axios.get('/UserPet/GetPetConfigOptions');
                    if (petResponse.data.code === 200) {
                        petConfigOptions.value = petResponse.data.data;
                    }

                    // 加载境界选项
                    await loadRealmOptions();

                } catch (error) {
                    console.error('加载选项失败：', error);
                }
            };

            const showAddModal = () => {
                isEdit.value = false;
                Object.assign(userPetForm.value, {
                    id: 0,
                    userId: null,
                    petNo: null,
                    image: null,
                    exp: 0,
                    hp: 100,
                    mp: 100,
                    atk: 10,
                    def: 10,
                    spd: 10,
                    state: null,
                    dodge: 10,
                    growth: 1.0,
                    hit: 10,
                    realm: '',
                    evolveCount: 0,
                    element: '',
                    status: '牧场',
                    isMain: false
                });
                // 使用Bootstrap 5的模态框API
                const modal = new bootstrap.Modal(document.getElementById('userPetModal'));
                modal.show();
            };

            const editUserPet = (userPet) => {
                isEdit.value = true;
                Object.assign(userPetForm.value, {
                    id: userPet.id,
                    userId: userPet.userId,
                    petNo: userPet.petNo,
                    image: userPet.image || null,
                    exp: userPet.exp || 0,
                    hp: userPet.hp || 100,
                    mp: userPet.mp || 100,
                    atk: userPet.atk || 10,
                    def: userPet.def || 10,
                    spd: userPet.spd || 10,
                    state: userPet.state || null,
                    dodge: userPet.dodge || 10,
                    growth: userPet.growth || 1.0,
                    hit: userPet.hit || 10,
                    realm: userPet.realm || '',
                    evolveCount: userPet.evolveCount || 0,
                    element: userPet.element || '',
                    status: userPet.status || '',
                    isMain: userPet.isMain || false
                });
                const modal = new bootstrap.Modal(document.getElementById('userPetModal'));
                modal.show();
            };

            // 验证宠物数量限制的方法
            const validatePetLimits = async () => {
                const userId = userPetForm.value.userId;
                const currentPetId = userPetForm.value.id; // 编辑时的当前宠物ID
                
                try {
                    // 获取用户当前的宠物列表
                    const response = await axios.post('/UserPet/GetList', {
                        userId: userId,
                        page: 1,
                        pageSize: 1000 // 获取所有宠物
                    });
                    
                    if (response.data.code !== 200) {
                        throw new Error('获取用户宠物列表失败');
                    }
                    
                    const userPets = response.data.data || [];
                    
                    // 过滤掉当前编辑的宠物（编辑模式下）
                    const otherPets = userPets.filter(pet => pet.id !== currentPetId);
                    
                    // 统计携带和主战宠物数量
                    const carryPets = otherPets.filter(pet => pet.status === '携带');
                    const mainPets = otherPets.filter(pet => pet.isMain);
                    const carryAndMainCount = carryPets.length + mainPets.length;
                    
                    // 检查主战宠物唯一性
                    if (userPetForm.value.isMain && mainPets.length > 0) {
                        throw new Error('该用户已有主战宠物，每个用户只能有一个主战宠物！请先将其他主战宠物设置为携带状态。');
                    }
                    
                    // 检查携带+主战宠物总数限制
                    let newCarryAndMainCount = carryAndMainCount;
                    if (userPetForm.value.status === '携带' || userPetForm.value.isMain) {
                        newCarryAndMainCount += 1;
                    }
                    
                    if (newCarryAndMainCount > 3) {
                        throw new Error(`携带和主战宠物总数不能超过3只！当前已有${carryAndMainCount}只，添加后将超出限制。`);
                    }
                    
                } catch (error) {
                    throw error;
                }
            };

            const saveUserPet = async () => {
                try {
                    saving.value = true;
                    
                    // 验证用户宠物数量限制
                    if (userPetForm.value.status === '携带' || userPetForm.value.isMain) {
                        await validatePetLimits();
                    }
                    
                    const url = isEdit.value ? '/UserPet/Update' : '/UserPet/Create';
                    
                    // 转换数据格式以匹配后端DTO
                    const requestData = {
                        ...userPetForm.value,
                        // 字段名转换为大驼峰
                        UserId: userPetForm.value.userId,
                        PetNo: userPetForm.value.petNo,
                        Image: userPetForm.value.image ? parseInt(userPetForm.value.image) : null,
                        Exp: userPetForm.value.exp,
                        Hp: userPetForm.value.hp,
                        Mp: userPetForm.value.mp,
                        Atk: userPetForm.value.atk,
                        Def: userPetForm.value.def,
                        Spd: userPetForm.value.spd,
                        State: userPetForm.value.state ? parseInt(userPetForm.value.state) : null,
                        Dodge: userPetForm.value.dodge,
                        Growth: userPetForm.value.growth,
                        Hit: userPetForm.value.hit,
                        Realm: userPetForm.value.realm,
                        EvolveCount: userPetForm.value.evolveCount,
                        Element: userPetForm.value.element,
                        Status: userPetForm.value.status,
                        IsMain: userPetForm.value.isMain
                    };
                    
                    // 删除小驼峰字段
                    delete requestData.userId;
                    delete requestData.petNo;
                    delete requestData.image;
                    delete requestData.exp;
                    delete requestData.hp;
                    delete requestData.mp;
                    delete requestData.atk;
                    delete requestData.def;
                    delete requestData.spd;
                    delete requestData.state;
                    delete requestData.dodge;
                    delete requestData.growth;
                    delete requestData.hit;
                    delete requestData.realm;
                    delete requestData.evolveCount;
                    delete requestData.element;
                    
                    const response = await axios.post(url, requestData);
                    
                    if (response.data.code === 200) {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('userPetModal'));
                        modal.hide();
                        loadUserPets();
                        alert(response.data.message || '保存成功');
                    } else {
                        alert(response.data.message || '保存失败');
                    }
                } catch (error) {
                    console.error('保存用户宠物失败：', error);
                    alert(error.message || '保存失败');
                } finally {
                    saving.value = false;
                }
            };

            const deleteUserPet = async (userPet) => {
                if (!confirm(`确定要删除宠物"${userPet.petName}"吗？删除后将无法恢复！`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/Delete', { id: userPet.id });
                    if (response.data.code === 200) {
                        loadUserPets();
                        alert('删除成功');
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除用户宠物失败：', error);
                    alert('删除失败');
                }
            };

            const viewDetail = (userPet) => {
                selectedUserPet.value = userPet;
                console.log('Selected pet:', userPet); // 调试日志
                // 使用原生Bootstrap 5 API
                const modal = new bootstrap.Modal(document.getElementById('detailModal'));
                modal.show();
            };

            const manageSkills = async (userPet) => {
                skillManagePet.value = userPet;
                skillForm.value.userPetId = userPet.id;
                await loadPetSkills(userPet.id);
                const modal = new bootstrap.Modal(document.getElementById('skillModal'));
                modal.show();
            };

            const evolvePet = async (userPet) => {
                if (!confirm(`确定要让宠物"${userPet.petName}"进化吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/Evolve', null, {
                        params: { userPetId: userPet.id }
                    });
                    if (response.data.code === 200) {
                        loadUserPets();
                        alert(response.data.message || '进化成功');
                    } else {
                        alert(response.data.message || '进化失败');
                    }
                } catch (error) {
                    console.error('宠物进化失败：', error);
                    alert('进化失败');
                }
            };

            const loadPetSkills = async (userPetId) => {
                try {
                    skillLoading.value = true;
                    const response = await axios.get(`/UserPet/GetSkills/${userPetId}`);
                    if (response.data.code === 200) {
                        petSkills.value = response.data.data || [];
                    } else {
                        console.error('获取宠物技能失败：', response.data.message);
                    }
                } catch (error) {
                    console.error('获取宠物技能失败：', error);
                } finally {
                    skillLoading.value = false;
                }
            };

            const showAddSkillForm = () => {
                showSkillForm.value = true;
                Object.assign(skillForm.value, {
                    userPetId: skillManagePet.value.id,
                    skillId: null,
                    skillLevel: 1
                });
            };

            const cancelAddSkill = () => {
                showSkillForm.value = false;
            };

            const addSkill = async () => {
                try {
                    skillSaving.value = true;
                    const response = await axios.post('/UserPet/AddSkill', skillForm.value);
                    
                    if (response.data.code === 200) {
                        showSkillForm.value = false;
                        loadPetSkills(skillManagePet.value.id);
                        alert(response.data.message || '添加技能成功');
                    } else {
                        alert(response.data.message || '添加技能失败');
                    }
                } catch (error) {
                    console.error('添加技能失败：', error);
                    alert('添加技能失败');
                } finally {
                    skillSaving.value = false;
                }
            };

            const upgradeSkill = async (skill) => {
                const newLevel = skill.skillLevel + 1;
                if (newLevel > 10) {
                    alert('技能等级已达到最高等级');
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/UpdateSkillLevel', null, {
                        params: { skillId: skill.id, skillLevel: newLevel }
                    });
                    
                    if (response.data.code === 200) {
                        loadPetSkills(skillManagePet.value.id);
                        alert('技能升级成功');
                    } else {
                        alert(response.data.message || '技能升级失败');
                    }
                } catch (error) {
                    console.error('技能升级失败：', error);
                    alert('技能升级失败');
                }
            };

            const deleteSkill = async (skill) => {
                if (!confirm(`确定要删除技能"${skill.skillName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/DeleteSkill', { id: skill.id });
                    if (response.data.code === 200) {
                        loadPetSkills(skillManagePet.value.id);
                        alert('删除技能成功');
                    } else {
                        alert(response.data.message || '删除技能失败');
                    }
                } catch (error) {
                    console.error('删除技能失败：', error);
                    alert('删除技能失败');
                }
            };

            const assignSkillsByConfig = async () => {
                if (!confirm('确定要自动分配宠物配置中的技能吗？')) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/AssignSkills', null, {
                        params: { userPetId: skillManagePet.value.id }
                    });
                    
                    if (response.data.code === 200) {
                        loadPetSkills(skillManagePet.value.id);
                        alert(response.data.message || '技能分配成功');
                    } else {
                        alert(response.data.message || '技能分配失败');
                    }
                } catch (error) {
                    console.error('技能分配失败：', error);
                    alert('技能分配失败');
                }
            };

            const closeModal = () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('userPetModal'));
                if (modal) modal.hide();
            };

            const closeDetailModal = () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('detailModal'));
                if (modal) modal.hide();
            };

            const closeSkillModal = () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('skillModal'));
                if (modal) modal.hide();
                showSkillForm.value = false;
            };

            // 工具方法
            const calculateLevel = (exp) => {
                return Math.floor(exp / 100) + 1;
            };

            const formatDateTime = (dateTime) => {
                if (!dateTime) return '';
                return new Date(dateTime).toLocaleString();
            };

            // ==================== 境界管理方法 ====================

            // 加载境界列表
            const loadRealms = async () => {
                realmLoading.value = true;
                try {
                    const requestData = {
                        ...realmQueryForm.value,
                        page: realmCurrentPage.value,
                        pageSize: realmPageSize.value
                    };
                    
                    const response = await axios.post('/UserPet/GetRealmList', requestData);
                    if (response.data.code === 200) {
                        realmsList.value = response.data.data || [];
                        realmTotalCount.value = response.data.total || 0;
                    } else {
                        console.error('获取境界列表失败：', response.data.message);
                    }
                } catch (error) {
                    console.error('获取境界列表失败：', error);
                } finally {
                    realmLoading.value = false;
                }
            };

            // 搜索境界
            const searchRealms = () => {
                realmCurrentPage.value = 1;
                loadRealms();
            };

            // 重置境界搜索
            const resetRealmSearch = () => {
                Object.assign(realmQueryForm.value, {
                    realmId: null,
                    realmName: '',
                    page: 1,
                    pageSize: 10
                });
                realmCurrentPage.value = 1;
                loadRealms();
            };

            // 重置境界表单
            const resetRealmForm = () => {
                realmForm.value = {
                    realmId: null,
                    realmName: ''
                };
            };

            // 显示新增境界模态框
            const showAddRealmModal = () => {
                isRealmEdit.value = false;
                resetRealmForm();
                const modal = new bootstrap.Modal(document.getElementById('realmModal'));
                modal.show();
            };

            // 显示编辑境界模态框
            const showEditRealmModal = async (realm) => {
                isRealmEdit.value = true;
                
                try {
                    const response = await axios.get(`/UserPet/GetRealmById?realmId=${realm.realmId}`);
                    if (response.data.code === 200) {
                        const data = response.data.data;
                        realmForm.value = {
                            realmId: data.realmId,
                            realmName: data.realmName
                        };
                        const modal = new bootstrap.Modal(document.getElementById('realmModal'));
                        modal.show();
                    } else {
                        alert('获取境界信息失败');
                    }
                } catch (error) {
                    console.error('获取境界信息出错：', error);
                    alert('获取境界信息失败');
                }
            };

            // 保存境界
            const saveRealm = async () => {
                // 验证表单
                if (!realmForm.value.realmId || !realmForm.value.realmName) {
                    alert('请填写必填字段');
                    return;
                }

                realmSaving.value = true;
                try {
                    const url = isRealmEdit.value ? '/UserPet/UpdateRealm' : '/UserPet/CreateRealm';
                    const response = await axios.post(url, realmForm.value);

                    if (response.data.code === 200) {
                        alert(isRealmEdit.value ? '境界更新成功' : '境界创建成功');
                        const modal = bootstrap.Modal.getInstance(document.getElementById('realmModal'));
                        modal.hide();
                        loadRealms();
                        // 重新加载境界选项
                        await loadRealmOptions();
                    } else {
                        alert(response.data.message || '操作失败');
                    }
                } catch (error) {
                    console.error('保存境界出错：', error);
                    alert('保存境界失败');
                } finally {
                    realmSaving.value = false;
                }
            };

            // 删除境界
            const deleteRealm = async (realm) => {
                if (realm.petCount > 0) {
                    alert(`该境界下还有 ${realm.petCount} 个宠物，无法删除！`);
                    return;
                }

                if (!confirm(`确定要删除境界"${realm.realmName}"吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/UserPet/DeleteRealm', {
                        realmId: realm.realmId
                    });

                    if (response.data.code === 200) {
                        alert('删除成功');
                        loadRealms();
                        // 重新加载境界选项
                        await loadRealmOptions();
                    } else {
                        alert(response.data.message || '删除失败');
                    }
                } catch (error) {
                    console.error('删除境界出错：', error);
                    alert('删除失败');
                }
            };

            // 境界分页
            const changeRealmPage = (page) => {
                if (page < 1 || page > realmTotalPages.value || page === realmCurrentPage.value) {
                    return;
                }
                realmCurrentPage.value = page;
                loadRealms();
            };

            // 关闭境界模态框
            const closeRealmModal = () => {
                const modal = bootstrap.Modal.getInstance(document.getElementById('realmModal'));
                if (modal) modal.hide();
            };



            // 生命周期
            onMounted(async () => {
                console.log('Vue应用已挂载');
                await loadOptions();
                await loadStatusOptions();
                await loadUserPets();
                // 初始加载境界列表 
                await loadRealms();
            });

            return {
                // 数据
                loading,
                saving,
                skillLoading,
                skillSaving,
                userPetsList,
                totalCount,
                currentPage,
                pageSize,
                isEdit,
                selectedUserPet,
                skillManagePet,
                petSkills,
                showSkillForm,
                userOptions,
                petConfigOptions,
                realmOptions,
                statusOptions,
                queryForm,
                userPetForm,
                skillForm,
                
                // 计算属性
                totalPages,
                visiblePages,
                
                // 方法
                loadUserPets,
                loadRealmOptions,
                loadStatusOptions,
                searchUserPets,
                resetQuery,
                resetForm,
                changePage,
                showAddModal,
                editUserPet,
                saveUserPet,
                deleteUserPet,
                viewDetail,
                manageSkills,
                evolvePet,
                loadPetSkills,
                showAddSkillForm,
                cancelAddSkill,
                addSkill,
                upgradeSkill,
                deleteSkill,
                assignSkillsByConfig,
                closeModal,
                closeDetailModal,
                closeSkillModal,
                calculateLevel,
                formatDateTime,
                
                // 境界管理相关
                realmLoading,
                realmSaving,
                realmsList,
                realmTotalCount,
                realmCurrentPage,
                realmPageSize,
                realmQueryForm,
                realmForm,
                isRealmEdit,
                realmTotalPages,
                realmVisiblePages,
                loadRealms,
                searchRealms,
                resetRealmSearch,
                showAddRealmModal,
                showEditRealmModal,
                saveRealm,
                deleteRealm,
                changeRealmPage,
                closeRealmModal,
                

            };
        }
    }).mount('#userPetApp');
</script> 